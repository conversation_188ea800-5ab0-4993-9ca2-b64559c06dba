# 成熟方案一：

![1740555552148](image/信创主机进阶方案/1740555552148.png)

![1740555576468](image/信创主机进阶方案/1740555576468.png)

![1740555596677](image/信创主机进阶方案/1740555596677.png)

### **一、规则匹配与LLM结合的优化**

1. **动态规则生成与语义分析**

   - 利用LLM的语义理解能力，自动分析日志中的异常模式并生成自定义规则。例如，通过AI模型识别新型攻击特征后，动态生成正则表达式或规则条件，直接写入Wazuh规则集（如 `/var/ossec/etc/rules/`）。
   - LLM可辅助优化正则表达式匹配的准确性。例如，当解码器正则匹配失败时（如提到的正则解析问题），通过LLM分析日志结构并建议修正后的表达式，减少误报或漏报。
2. **基于上下文的告警分级**

   - 结合LLM的上下文理解能力，对告警级别（`level`）进行动态调整。例如，将高频低风险事件（如多次登录失败）通过LLM关联上下文（如IP地理位置、用户行为基线）自动升级为高风险告警（Level 10+），优化邮件告警的触发逻辑。
3. **自然语言驱动的规则测试及运维**

   - 使用类似ChatGPT的模型构建交互式工具，允许安全分析师通过自然语言描述场景（如"检测SSH暴力破解"），自动生成规则测试用例并通过 `wazuh-logtest`验证匹配效果。

### **二、AI增强的日志分析场景**

4. **自然语言查询与响应**

   - 集成LLM API（如OpenAI或本地模型），构建CLI工具或仪表盘插件，支持以自然语言查询日志。例如，输入"过去24小时有哪些异常登录行为？"可直接生成聚合结果和关联分析，无需手动编写SQL或KQL。
5. **告警摘要与根因推断**

   - 对Wazuh告警（如 `Security events`模块中的事件）使用LLM生成多语言摘要，并推测潜在攻击链。例如，将SSH爆破告警与后续提权行为关联，输出"疑似横向移动尝试"的结论。
6. **自动化响应建议**

   - 基于LLM的历史事件学习，为特定告警提供响应策略。例如，针对"DDoS检测"告警，自动生成"隔离目标IP并启动流量清洗"的操作指令，并通过Wazuh主动响应（Active Response）执行。

该集成利用了以下组件：

- **Wazuh Dashboard**：作为与 AI 进行交互的用户界面。
- **OpenSearch Dashboards 插件**：启用可观察性、机器学习和聊天功能。
- **CLI程序中间件**：通过CLI程序部署的AI驱动NLP模型具有理解日志条目中嵌入的情境细微差别的能力，从而提高安全分析的深度和准确性。。
- **Wazuh Indexer**：管理数据索引和机器学习操作的插件集成。

### 三、`结合安全产品实现自动化分析研判，自动处置`

> [!Tips]

可在服务器端部署相关小模型：可以使用JRip算法（训练来源于CICDDoS2019数据集）进行动态规则编写以检测DDoS攻击（对来自agent的日志数据进行解码，提取出模型所需要的特征），部署在wazuh服务器上，当然我觉得也可以部署其他相关的web安全以及系统安全模型进行检测。此外，上述的部署后的模型，需要进行SWaT数据集的评估，或者是进行相关的白帽测试。

### 四、`如何赋予wazuh ai能力进行分析研判`

结合Opensearch进行大语言模型的研判分析

# 成熟方案二：

```markdown
- 互联网
  - 防火墙1
    - Zabbix/Wazuh代理1
      - 办公室1
        - 交换机
          - 无线网络
          - 局域网
          - 网络附加存储
        - 将数据发送至Zabbix/Wazuh服务器
      - 连接至AWS/微软Azure云（搭载Zabbix/Wazuh服务器的虚拟专用服务器）
  - 防火墙2
    - Zabbix/Wazuh代理2
      - 办公室2
        - 交换机
          - 无线网络
          - 局域网
          - 网络附加存储
        - 将数据发送至Zabbix/Wazuh服务器
      - 连接至AWS/微软Azure云（搭载Zabbix/Wazuh服务器的虚拟专用服务器）
  - Zabbix/Wazuh代理3
    - 树莓派
      - 水处理厂
        - 可编程逻辑控制器
        - 数据采集与监视控制系统
      - 将数据发送至Zabbix/Wazuh服务器
  - Zabbix/Wazuh服务器（AWS/微软Azure云）
    - 特征提取（大语言模型/人工智能解决方案）
      - 从Zabbix/Wazuh日志中收集并预处理数据
      - 将数据整理成结构化摘要
      - 为大语言模型的分析准备数据
    - 异常检测（大语言模型/人工智能解决方案）
      - 使用大语言模型分析预处理后的数据
      - 识别与正常运行模式的偏差
      - 采用特定提示来引导分析
    - 威胁评估（大语言模型/人工智能解决方案）
      - 评估检测到的异常的严重程度
      - 考虑对网络安全和运营的潜在影响
      - 生成用于评估威胁严重程度的提示
    - 解决方案生成（大语言模型/人工智能解决方案）
      - 根据威胁严重程度提出可行的解决方案
      - 推荐针对特定安全问题的策略
      - 使用提示指令大语言模型提供缓解措施
    - 输出/可行建议（大语言模型/人工智能解决方案）
      - 向Zabbix传达建议以用于自动化脚本
      - 提醒管理员采取进一步行动
      - 显示可行建议和通知
```

---

# wazuh部署

以下是在 **Ubuntu 22.04 LTS** 上安装单节点 Wazuh 服务器（集成管理端、索引器和仪表盘）的详细步骤：

---

### **1. 系统准备**

#### **1.1 系统要求**

- **硬件**：至少 4核 CPU、8GB 内存、50GB 存储（AI场景建议配置 GPU 加速）
- **网络**：开放端口 `443/TCP`（仪表盘）、`9200/TCP`（索引器）、`1514-1515/TCP/UDP`（Agent通信）

### Wazuh安装混合方案：代理下载+本地安装

1. 根据您提供的情况，我设计了一个混合方案，通过代理下载必要的软件包，但在本地执行安装过程，并利用已下载的wazuh-template.json文件。

#### 完整安装脚本

创建以下脚本文件install-wazuh-hybrid.sh：

```bash
#!/bin/bash

# 确保正确的工作目录
cd ~

# 设置颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 显示消息函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}
log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}
log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 确认本地wazuh-template.json文件存在
TEMPLATE_FILE="$HOME/桌面/wazuh-template.json"
if [ ! -f "$TEMPLATE_FILE" ]; then
    log_error "桌面上找不到wazuh-template.json文件!"
    exit 1
fi
log_info "已找到wazuh-template.json文件: $TEMPLATE_FILE"

# 复制到临时目录
WORK_DIR="/tmp/wazuh-hybrid-install"
mkdir -p "$WORK_DIR"
cp "$TEMPLATE_FILE" "$WORK_DIR/"
log_info "复制wazuh-template.json到临时目录"

# 通过代理下载软件包
log_info "开始通过代理下载必要的软件包..."
proxychains curl -O https://packages.wazuh.com/4.11/wazuh-install.sh
chmod +x wazuh-install.sh
log_info "安装脚本下载完成"

# 创建修改版安装脚本
log_info "创建修改版安装脚本..."
cp wazuh-install.sh "$WORK_DIR/wazuh-install-modified.sh"
chmod +x "$WORK_DIR/wazuh-install-modified.sh"

# 修改脚本，使用本地template文件
sed -i "s|filebeat_wazuh_template=\"https://raw.githubusercontent.com/wazuh/wazuh/.*\"|filebeat_wazuh_template=\"file://$WORK_DIR/wazuh-template.json\"|" "$WORK_DIR/wazuh-install-modified.sh"

# 修改索引器初始化函数，避免代理问题
sed -i 's/function indexer_initialize() {/function indexer_initialize() {\n    # 确保直接连接本地服务\n    common_logger "Initializing Wazuh indexer cluster security settings."\n    # 不检查连接\n    local e_code=0\n/' "$WORK_DIR/wazuh-install-modified.sh"

# 如果已有证书文件，复制到工作目录
if [ -f "wazuh-install-files.tar" ]; then
    cp wazuh-install-files.tar "$WORK_DIR/"
    log_info "复制证书文件到工作目录"
fi

# 清理旧的安装（如有需要）
log_warn "即将清理旧的Wazuh安装（如果有）"
read -p "是否继续？(y/n): " confirm
if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
    sudo systemctl stop wazuh-indexer wazuh-manager wazuh-dashboard filebeat 2>/dev/null
    sudo apt purge wazuh-indexer wazuh-manager wazuh-dashboard filebeat -y 2>/dev/null
    sudo rm -rf /etc/wazuh-indexer /etc/wazuh-dashboard /var/lib/wazuh-indexer /var/lib/wazuh-dashboard 2>/dev/null
    log_info "清理完成"
else
    log_info "跳过清理步骤"
fi

# 执行安装（不使用代理）
log_info "开始安装Wazuh（不使用代理）..."
cd "$WORK_DIR"
sudo ./wazuh-install-modified.sh -a

# 检查安装结果
if [ $? -eq 0 ]; then
    log_info "Wazuh安装成功完成！"
else
    log_error "安装过程中遇到错误。"
  
    # 提供额外的修复选项
    log_warn "是否尝试手动初始化索引器？(y/n): "
    read fix_confirm
    if [ "$fix_confirm" = "y" ] || [ "$fix_confirm" = "Y" ]; then
        log_info "尝试手动初始化索引器..."
        sudo -u wazuh-indexer JAVA_HOME=/usr/share/wazuh-indexer/jdk/ \
            OPENSEARCH_CONF_DIR=/etc/wazuh-indexer \
            /usr/share/wazuh-indexer/plugins/opensearch-security/tools/securityadmin.sh \
            -cd /etc/wazuh-indexer/opensearch-security -icl -p 9200 -nhnv \
            -cacert /etc/wazuh-indexer/certs/root-ca.pem \
            -cert /etc/wazuh-indexer/certs/admin.pem \
            -key /etc/wazuh-indexer/certs/admin-key.pem -h 127.0.0.1
  
        log_info "现在尝试安装其他组件..."
        sudo ./wazuh-install-modified.sh -ws server-1
        sudo ./wazuh-install-modified.sh -wd dashboard-1
    fi
fi

# 清理临时文件
log_info "清理临时文件..."
cd ~
rm -rf "$WORK_DIR"

log_info "安装过程完成!"
```

#### 使用说明

* 将上述脚本保存为 install-wazuh-hybrid.sh
* 设置执行权限：

```
   chmod +x install-wazuh-hybrid.sh
```

运行脚本：

```
   ./install-wazuh-hybrid.sh
```

#### 工作原理（待优化：脚本中的三个组件，可以优化为proxychains下载）

这个脚本：

* 验证预先条件：检查桌面上是否有wazuh-template.json文件
* 组织工作空间：创建临时目录，复制必要文件
* 代理下载：通过proxychains下载安装脚本
* 修改脚本：
* 将GitHub文件URL替换为本地文件路径
* 修改索引器初始化函数避免连接检查
* 无代理安装：直接执行修改后的安装脚本，避开代理
* 错误恢复：如果安装失败，提供手动初始化选项

---

### **5. 安装后操作**

#### **5.1 部署 Wazuh Agent（版本为4.11）**

```bash
# 在服务器本机安装 Agent（用于监控自身）
sudo apt install wazuh-agent

# 配置 Agent 连接到 Manager
sudo vim /var/ossec/etc/ossec.conf
---
<client>
  <server>
    <address>127.0.0.1</address>  <!-- 修改为 Manager IP -->
  </server>
</client>
---

# 启动 Agent
sudo systemctl enable wazuh-agent
sudo systemctl start wazuh-agent
```

#### **5.2 验证日志采集**

1. 在仪表盘中查看数据：
   - 导航到 **Security → Overview**，确认是否有日志事件。
2. 手动触发测试告警：
   ```bash
   # 生成一条 SSH 登录失败日志
   logger "Failed password for root from ************* port 22"
   ```

---

### **6. AI 集成准备（可选）**

#### **6.1 安装 Python 开发环境**

```bash
sudo apt install python3-pip python3-venv
pip3 install wazuh==4.7.0   # Wazuh Python SDK
pip3 install transformers torch
```

#### **6.2 配置动态规则热加载**

创建脚本 `/opt/wazuh-ai/reload_rules.py`：

```python
from wazuh import Wazuh
import time

def reload_rules():
    w = Wazuh()
    w.manager.restart()
    print(f"[{time.ctime()}] Rules reloaded.")

if __name__ == "__main__":
    reload_rules()
```

---

### **常见问题排查**

根据你的操作日志，我来整理一个解决 Wazuh Dashboard 配置问题的笔记：

```markdown:Wazuh
# Wazuh Dashboard 配置问题解决方案

## 1. 问题诊断
### 1.1 初始错误分析
- 服务启动失败的主要错误：
  - 证书文件访问权限问题
  - 配置文件中存在无效配置项 `cache.maxAge`
  - 配置文件权限问题

### 1.2 关键错误信息
```log
FATAL Error: Unknown configuration key(s): "cache.maxAge"
Error: ENOENT: no such file or directory, open '/etc/wazuh-dashboard/certs/dashboard-key.pem'
```

#### 2. 解决步骤

##### 2.1 证书配置

```bash
# 创建并设置证书目录权限
sudo mkdir -p /etc/wazuh-dashboard/certs/
sudo chown -R wazuh-dashboard:wazuh-dashboard /etc/wazuh-dashboard/certs/
sudo chmod 500 /etc/wazuh-dashboard/certs

# 复制证书文件
sudo cp /etc/wazuh-indexer/certs/dashboard-key.pem /etc/wazuh-dashboard/certs/
sudo cp /etc/wazuh-indexer/certs/dashboard.pem /etc/wazuh-dashboard/certs/
sudo cp /etc/wazuh-indexer/certs/root-ca.pem /etc/wazuh-dashboard/certs/

# 设置证书文件权限
sudo chmod 400 /etc/wazuh-dashboard/certs/*.pem
```

##### 2.2 配置文件修正

- 移除无效配置项 `cache.maxAge`
- 更新配置文件权限：

```bash
sudo chown wazuh-dashboard:wazuh-dashboard /etc/wazuh-dashboard/opensearch_dashboards.yml
sudo chmod 640 /etc/wazuh-dashboard/opensearch_dashboards.yml
```

##### 2.3 用户权限配置

```bash
# 将当前用户添加到 wazuh-dashboard 组
sudo usermod -a -G wazuh-dashboard $USER
newgrp wazuh-dashboard
```

#### 3. 服务重启

```bash
sudo systemctl daemon-reload
sudo systemctl restart wazuh-dashboard
```

#### 4. 验证步骤

##### 4.1 服务状态检查

```bash
sudo systemctl status wazuh-dashboard
```

##### 4.2 日志检查

```bash
sudo journalctl -u wazuh-dashboard -f
```

##### 4.3 成功标志

- 服务状态显示 "active (running)"
- 日志显示 "Server running at https://0.0.0.0:5601"
- 成功创建索引 "wazuh-monitoring-2025.10w"

#### 5. 环境信息

- Node.js 版本: v18.19.0
- 系统: Ubuntu 22.04 LTS
- Wazuh Dashboard 端口: 5601

#### 6. 注意事项

1. 证书文件权限必须严格控制（400）
2. 配置文件需要正确的所有权和权限
3. 确保使用有效的配置项
4. 服务重启后要检查日志确认启动状态

```

这个笔记总结了问题的诊断、解决步骤和验证方法，可以作为将来解决类似问题的参考。特别强调了权限设置和配置文件的正确性，这些是解决此类问题的关键点。
```

### **总结**

通过以上步骤，你已经完成：

- 单节点 Wazuh 服务器（Manager + Indexer + Dashboard）部署
- 本地 Agent 监控配置
- 基础 AI 集成环境准备

下一步可进行：

1. **AI 规则引擎开发**：将本地 LLM 模型与 Wazuh 的 `decoders` 和 `rules` 目录集成。
2. **告警优化**：在 `/var/ossec/etc/rules/local_rules.xml` 中添加动态生成的规则。
3. **性能扩展**：使用 Docker 或 Kubernetes 分离 AI 推理服务与 Wazuh 核心组件。

---

# 安装SNORT教程

* [ ] 未来可借鉴的网络安全工具

```bash
sudo rm /var/ossec/etc/rules/100002-suricata.xml
sudo rm /var/ossec/etc/rules/100099-Modsecurity.xml
sudo rm /var/ossec/etc/rules/200300-packetbeat_rules.xml
sudo rm /var/ossec/etc/rules/200360-docker_falco_rules.xml
sudo rm /var/ossec/etc/rules/200400-nmap-scan_rules.xml
```

### 更新软件源

sudo apt update

### 安装依赖

sudo apt install -y build-essential libpcap-dev libpcre3-dev libnet1-dev \

zlib1g-dev liblzma-dev openssl libssl-dev libnghttp2-dev libluajit-5.1-dev \

libdumbnet-dev bison flex libhwloc-dev

### 安装Snort

sudo apt install -y snort

## 一、检查Snort配置文件

```
查看主配置文件
sudo nano /etc/snort/snort.conf
```

主要检查以下配置是否正确：

* 确认HOME_NET设置为您选择的值(***************/32)
* 确认EXTERNAL_NET设置为!$HOME_NET
* 确认规则路径和日志目录设置正确

## 二、创建并配置必要目录

```
# 创建Snort工作目录
sudo mkdir -p /var/log/snort
sudo mkdir -p /etc/snort/rules
sudo mkdir -p /etc/snort/so_rules
sudo mkdir -p /etc/snort/preproc_rules

# 设置权限
sudo chmod -R 750 /var/log/snort
sudo chmod -R 750 /etc/snort/rules
```

## 三、启用必要规则

Snort默认安装包括了一些规则，需要确认这些规则已启用：

```
# 查看默认规则目录
ls -la /etc/snort/rules

# 编辑配置文件启用规则
sudo nano /etc/snort/snort.conf
```

在snort.conf中找到include部分，确保以下行没有被注释：

```
include $RULE_PATH/local.rules
include $RULE_PATH/bad-traffic.rules
include $RULE_PATH/exploit.rules
include $RULE_PATH/scan.rules
include $RULE_PATH/dos.rules
```

添加alert_fast输出格式，使Snort生成人类可读的告警文件,在unified2输出配置下方(大约536行后)添加以下行：：

```
# 添加可读告警输出
output alert_fast: alert
```

## 四、创建自定义规则文件

```
# 创建本地规则文件
sudo nano /etc/snort/rules/local.rules
```

添加一些基本规则，例如：

```
# 检测ICMP流量(Ping)
alert icmp $EXTERNAL_NET any -> $HOME_NET any (msg:"ICMP连接尝试"; sid:10000001; rev:1;)

# 检测端口扫描尝试
alert tcp $EXTERNAL_NET any -> $HOME_NET any (msg:"可能的端口扫描"; detection_filter:track by_src, count 30, seconds 60; sid:10000002; rev:1;)

# 检测SSH暴力破解
alert tcp $EXTERNAL_NET any -> $HOME_NET 22 (msg:"SSH暴力破解尝试"; flow:to_server; threshold:type threshold, track by_src, count 5, seconds 60; sid:10000003; rev:1;)
```

## 五、测试Snort配置

```
# 测试配置文件语法
sudo snort -T -c /etc/snort/snort.conf -i ens33
```

如果配置正确，您应该看到类似"Snort successfully validated the configuration!"的消息。

## 六、创建Snort服务文件

```
# 创建systemd服务文件
sudo nano /etc/systemd/system/snort.service
```

添加以下内容：

```
[Unit]
Description=Snort NIDS Daemon
After=network.target

[Service]
Type=simple
ExecStart=/usr/sbin/snort -q -u snort -g snort -c /etc/snort/snort.conf -i ens33
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
```

## 七、启动Snort服务

```
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动Snort服务
sudo systemctl enable snort
sudo systemctl start snort

# 检查服务状态
sudo systemctl status snort
```

## 八、配置Snort与Wazuh集成

确保Snort告警可以被Wazuh收集：

```
#编辑Wazuh agent配置
sudo nano /var/ossec/etc/ossec.conf
```

添加以下配置段：

```
<localfile>
  <log_format>snort-full</log_format>
  <location>/var/log/snort/alert</location>
</localfile>
```

然后重启Wazuh agent：

```
sudo systemctl restart wazuh-agent
```

## 九、测试Snort检测功能

执行一个简单的测试，例如从另一台机器ping您的系统：

```
# 从另一台机器
ping ***************
```

然后检查Snort告警日志：

```
sudo tail -f /var/log/snort/alert
```

您应该能看到相关的ICMP告警记录。

---

# Agent与wazuh服务器测试

## 一、终端安全综合测试方案

### 1. 网络层面安全测试

| 测试类型 | 具体操作                 | Wazuh预期反馈                            |
| -------- | ------------------------ | ---------------------------------------- |
| 端口扫描 | 使用nmap扫描agent主机    | 触发"Multiple connection attempts"警报   |
| SYN洪水  | 使用hping3执行SYN flood  | 触发"SYN flood"或网络异常警报            |
| 异常连接 | 尝试连接敏感端口(如3389) | 触发"Multiple failed login attempts"警报 |
| DNS隧道  | 建立DNS隧道通信          | 可能触发"Abnormal DNS traffic"警报       |

### 2. 主机安全测试

| 测试类型 | 具体操作                | Wazuh预期反馈                              |
| -------- | ----------------------- | ------------------------------------------ |
| 暴力破解 | 多次尝试登录系统账户    | 触发"Multiple authentication failures"警报 |
| 权限提升 | 执行sudo提权操作        | 触发"Privilege escalation"或相关审计警报   |
| 敏感命令 | 执行敏感系统命令如fdisk | 触发"Potentially dangerous command"警报    |
| 进程异常 | 创建异常进程或改名进程  | 触发"New process created"或进程异常警报    |

### 3. 文件监控测试

| 测试类型 | 具体操作                    | Wazuh预期反馈                                 |
| -------- | --------------------------- | --------------------------------------------- |
| 配置修改 | 修改系统关键配置文件        | 触发"File modification"警报                   |
| 敏感访问 | 访问/etc/passwd等敏感文件   | 触发"Access to sensitive file"警报            |
| 文件创建 | 在敏感目录创建可执行文件    | 触发"File created in sensitive directory"警报 |
| 权限变更 | 修改关键文件权限(chmod 777) | 触发"File permissions changed"警报            |

### 4. 恶意软件测试

| 测试类型    | 具体操作                | Wazuh预期反馈                 |
| ----------- | ----------------------- | ----------------------------- |
| EICAR测试   | 下载EICAR测试文件       | 触发"Malware detected"警报    |
| 反弹Shell   | 创建基本反弹shell脚本   | 触发网络连接和可疑进程警报    |
| Rootkit测试 | 运行无害rootkit模拟工具 | 触发完整性检查异常警报        |
| 加密挖矿    | 模拟加密货币挖矿行为    | 触发CPU异常使用和可疑连接警报 |

## 二、SOCFortress Wazuh规则安装

### 2.1 规则介绍

SOCFortress Wazuh规则是一套专为Wazuh平台优化的高级安全检测规则集，提供以下优势：

- 覆盖更广泛的攻击检测场景
- 更精确的威胁识别能力
- 减少误报和漏报
- 针对最新威胁技术的持续更新
- 与MITRE ATT&CK框架对齐的检测策略

规则集主要包含两部分：基础规则文件(`200980-socfortress.xml`)和补充规则文件(`700100-socfortress_added.xml`)，涵盖了从文件系统异常到复杂攻击链检测的多种场景。

### 2.2 安装前准备

#### 2.2.1 环境要求

- Wazuh Manager 4.x或更高版本
- 完整的管理员/root权限
- Git工具已安装
- 稳定的互联网连接（或可用的代理服务）
- 建议对当前规则进行备份

#### 2.2.2 备份当前规则

在安装新规则前，建议备份当前规则配置：

```bash
# 手动备份当前规则
sudo cp -r /var/ossec/etc/rules /var/ossec/etc/rules.bak
```

### 2.3 安装过程

由于网络限制，需要通过代理下载和安装SOCFortress规则。以下是详细步骤：

#### 2.3.1 下载安装脚本

通过代理下载安装脚本：

```bash
proxychains curl -o ~/wazuh_socfortress_rules.sh https://raw.githubusercontent.com/socfortress/Wazuh-Rules/main/wazuh_socfortress_rules.sh
chmod +x ~/wazuh_socfortress_rules.sh
```

#### 2.3.2 安装脚本执行

使用代理执行安装脚本，确保GitHub仓库克隆成功：

```bash
sudo proxychains bash ~/wazuh_socfortress_rules.sh -y
```

安装过程会执行以下主要操作：

1. 验证环境（检查Git是否存在）
2. 备份当前规则到 `/tmp/wazuh_rules_backup/`
3. 克隆SOCFortress规则仓库
4. 移动解码器文件到Wazuh解码器目录
5. 移动规则文件到Wazuh规则目录
6. 设置文件权限
7. 重启Wazuh Manager服务
8. 执行健康检查

#### 2.3.3 安装日志示例

以下是一个成功安装的日志示例：

```
03/21/2025 16:22:22 INFO: Confirmation skipped with -y flag
03/21/2025 16:22:22 INFO: Git package found. Continuing...
03/21/2025 16:22:23 INFO: Beginning the installation process
03/21/2025 16:22:23 INFO: Backing up current rules into /tmp/wazuh_rules_backup/
正克隆到 '/tmp/Wazuh-Rules'...
|DNS-request| github.com 
|S-chain|-<>-*************:7890-<><>-*******:53-<><>-OK
|DNS-response| github.com is **************
|S-chain|-<>-*************:7890-<><>-**************:443-<><>-OK
remote: Enumerating objects: 2968, done.
remote: Counting objects: 100% (685/685), done.
remote: Compressing objects: 100% (184/184), done.
remote: Total 2968 (delta 584), reused 501 (delta 501), pack-reused 2283 (from 1)
接收对象中: 100% (2968/2968), 11.03 MiB | 8.82 MiB/s, 完成.
处理 delta 中: 100% (1395/1395), 完成.
|DNS-response|: xiegd-virtual-machine does not exist
03/21/2025 16:22:26 INFO: Moving decoder decoder-linux-sysmon.xml to decoders directory
03/21/2025 16:22:26 INFO: Moving decoder yara_decoders.xml to decoders directory
03/21/2025 16:22:26 INFO: Moving decoder auditd_decoders.xml to decoders directory
03/21/2025 16:22:26 INFO: Moving decoder naxsi-opnsense_decoders.xml to decoders directory
03/21/2025 16:22:26 INFO: Moving decoder maltrail_decoders.xml to decoders directory
03/21/2025 16:22:26 INFO: Moving decoder decoder-manager-logs.xml to decoders directory
WAZUH_VERSION="v4.11.1"
WAZUH_REVISION="41112"
WAZUH_TYPE="server"
03/21/2025 16:22:26 INFO: Rules downloaded, attempting to restart the Wazuh-Manager service
03/21/2025 16:22:26 INFO: Restarting wazuh-manager using systemd...
systemctl: 未识别的选项 "--debug"
03/21/2025 16:22:26 ERROR: Wazuh-manager could not be restarted. Please check /var/ossec/logs/ossec.log for details.
03/21/2025 16:22:26 ERROR: Attempting to restore backed up rules...
03/21/2025 16:22:26 INFO: Restarting wazuh-manager using systemd...
systemctl: 未识别的选项 "--debug"
03/21/2025 16:22:26 ERROR: Wazuh-manager could not be restarted. Please check /var/ossec/logs/ossec.log for details.
03/21/2025 16:22:26 INFO: Performing a health check
03/21/2025 16:22:26 INFO: Restarting wazuh-manager using systemd...
systemctl: 未识别的选项 "--debug"
03/21/2025 16:22:26 ERROR: Wazuh-manager could not be restarted. Please check /var/ossec/logs/ossec.log for details.
03/21/2025 16:22:46 INFO: Wazuh-Manager Service is healthy. Thanks for checking us out :)
03/21/2025 16:22:46 INFO: Get started with our free-for-life tier here: https://www.socfortress.co/trial.html Happy Defending!
03/21/2025 16:22:46 INFO: Installation process completed
```

### 2.4 安装后验证

安装完成后，执行以下步骤验证安装结果：

#### 2.4.1 手动重启Wazuh服务

由于安装脚本中的systemctl命令参数错误，建议手动重启Wazuh管理器服务：

```bash
sudo systemctl restart wazuh-manager
```

#### 2.4.2 检查Wazuh服务状态

```bash
sudo systemctl status wazuh-manager
```

正常输出示例：

```
● wazuh-manager.service - Wazuh manager
     Loaded: loaded (/lib/systemd/system/wazuh-manager.service; enabled; vendor preset: enabled)
    Drop-In: /etc/systemd/system/wazuh-manager.service.d
             └─timeout.conf
     Active: active (running) since Fri 2025-03-21 16:25:40 CST; 16s ago
    Process: 165741 ExecStart=/usr/bin/env /var/ossec/bin/wazuh-control start (code=exited, status=0/SUCCESS)
```

#### 2.4.3 检查SOCFortress规则文件

验证SOCFortress规则文件是否存在：

```bash
sudo ls -la /var/ossec/etc/rules/ | grep -i socfortress
```

正常输出示例：

```
-rw-rw---- 1 <USER> <GROUP>  1547  3月 21 16:22 200980-socfortress.xml
-rw-rw---- 1 <USER> <GROUP>   794  3月 21 16:22 700100-socfortress_added.xml
```

#### 2.4.4 测试规则语法是否正确

使用Wazuh的日志测试工具检查规则语法：

```bash
sudo /var/ossec/bin/wazuh-logtest
```

### 2.5 常见问题及解决方案

#### 2.5.1 安装过程中的systemctl参数错误

**问题**：安装脚本使用了未识别的 `--debug`选项导致systemctl命令执行错误。

**解决方案**：

- 错误信息可以忽略，不影响规则文件的安装
- 手动重启Wazuh管理器服务以确保规则生效：
  ```bash
  sudo systemctl restart wazuh-manager
  ```

#### 2.5.2 GitHub访问问题

**问题**：无法直接访问GitHub，导致规则仓库克隆失败。

**解决方案**：

- 使用代理工具，例如proxychains：
  ```bash
  sudo proxychains bash ~/wazuh_socfortress_rules.sh -y
  ```
- 配置git全局代理：
  ```bash
  git config --global http.proxy http://proxy-server:port
  git config --global https.proxy http://proxy-server:port
  ```

#### 2.5.3 权限问题

**问题**：无法访问 `/var/ossec/etc/rules/`目录。

**解决方案**：

- 使用sudo运行命令：
  ```bash
  sudo ls -la /var/ossec/etc/rules/
  ```
- 检查当前用户是否在wazuh组中：
  ```bash
  groups
  sudo usermod -a -G wazuh $(whoami)
  ```

### 2.6 后续步骤

成功安装SOCFortress规则后，建议执行以下步骤：

1. 查看规则内容，了解新增的检测能力
2. 通过测试场景验证规则有效性
3. 调整告警级别或规则条件，适应特定环境需求
4. 定期更新规则集，获取最新威胁检测能力

### 3.0 SOCFortress规则遇到的部分问题（无法触发7级以上的log）

```
根据上述的agent测试脚本，以下是wazuh服务端的log信息，几乎都是五级和七级，没有出现其他更高的等级，请进行专业的分析：
xiegd@xiegd-virtual-machine:~/桌面$ sudo tail -f /var/ossec/logs/alerts/alerts.log | grep -A 10 -B 2 "socfortress"
[sudo] xiegd 的密码： 
2025 Mar 24 11:15:20 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/home/<USER>/Desktop/socfortress_test_controller.sh' modified
Mode: realtime
Changed attributes: permission
Permissions changed from 'rw-r--r--' to 'rwxr-xr-x'

Attributes:
 - Size: 10225
 - Permissions: rwxr-xr-x
 - Date: Mon Mar 24 11:11:05 2025
 - Inode: 1045259
 - User: root (0)
--
2025 Mar 24 11:15:28 (xiegd-PC) any->syscheck
Rule: 554 (level 5) -> 'File added to the system.'
File '/etc/socfortress-security.conf' added
Mode: realtime

Attributes:
 - Size: 0
 - Permissions: rw-r--r--
 - Date: Mon Mar 24 11:15:28 2025
 - Inode: 1050937
 - User: root (0)
 - Group: root (0)
 - MD5: d41d8cd98f00b204e9800998ecf8427e
--
2025 Mar 24 11:15:28 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/socfortress-security.conf' modified
Mode: realtime
Changed attributes: size,md5,sha1,sha256
Size changed from '0' to '47'
Old md5sum was: 'd41d8cd98f00b204e9800998ecf8427e'
New md5sum is : '2144efc5e01221bda131e64d6c9df1c7'
Old sha1sum was: 'da39a3ee5e6b4b0d3255bfef95601890afd80709'
New sha1sum is : '1368fde7ba24dc1e8bdccb28386e2099626c8315'
Old sha256sum was: 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'
New sha256sum is : 'f4a390af1fa74bbef2b6e9d47524e13c7760702c409f695432ca47dcb03cea77'

--
2025 Mar 24 11:15:28 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/socfortress-security.conf' modified
Mode: realtime
Changed attributes: size,mtime,md5,sha1,sha256
Size changed from '47' to '67'
Old modification time was: '1742786128', now it is '1742786129'
Old md5sum was: '2144efc5e01221bda131e64d6c9df1c7'
New md5sum is : '8e9373ec18d8887adf725ac8b4c28e28'
Old sha1sum was: '1368fde7ba24dc1e8bdccb28386e2099626c8315'
New sha1sum is : '03b783d9fbe5db3b549549f156170d4b27feb9c5'
Old sha256sum was: 'f4a390af1fa74bbef2b6e9d47524e13c7760702c409f695432ca47dcb03cea77'
New sha256sum is : 'f2ca250cd36c09328a29a62e251e5d9c4fcadaf05e765813e4dc4b4a3038dd49'
--
2025 Mar 24 11:15:28 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/socfortress-security.conf' modified
Mode: realtime
Changed attributes: size,md5,sha1,sha256
Size changed from '67' to '91'
Old md5sum was: '8e9373ec18d8887adf725ac8b4c28e28'
New md5sum is : '863f68bbd13c0ce67248925c50962192'
Old sha1sum was: '03b783d9fbe5db3b549549f156170d4b27feb9c5'
New sha1sum is : '1ea8bc4c5044f2aa54b4c2a6cf89eb94cb56a381'
Old sha256sum was: 'f2ca250cd36c09328a29a62e251e5d9c4fcadaf05e765813e4dc4b4a3038dd49'
New sha256sum is : 'ff2155cbf953d475e95b81abbcfabb87f6146c09811d0b53ea4ae5f23cf71463'

--
2025 Mar 24 11:15:33 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/socfortress-security.conf' modified
Mode: realtime
Changed attributes: size,mtime,md5,sha1,sha256
Size changed from '91' to '110'
Old modification time was: '1742786129', now it is '1742786134'
Old md5sum was: '863f68bbd13c0ce67248925c50962192'
New md5sum is : '89240759cfa421f0d56d0f0ac9f233c0'
Old sha1sum was: '1ea8bc4c5044f2aa54b4c2a6cf89eb94cb56a381'
New sha1sum is : 'e1b566738be04a15347675d6a0717eb5d43f5509'
Old sha256sum was: 'ff2155cbf953d475e95b81abbcfabb87f6146c09811d0b53ea4ae5f23cf71463'
New sha256sum is : 'd0d8475574daec0ce4cf2dfd8b183801ae771495946c7092a4af561a50f794d7'
--
2025 Mar 24 11:15:33 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/socfortress-security.conf' modified
Mode: realtime
Changed attributes: size,md5,sha1,sha256
Size changed from '110' to '133'
Old md5sum was: '89240759cfa421f0d56d0f0ac9f233c0'
New md5sum is : '10f8ce1a4e12b3cff6df66906ef045d6'
Old sha1sum was: 'e1b566738be04a15347675d6a0717eb5d43f5509'
New sha1sum is : 'e8f9afd3f94ee8794c22fd120dba8457b5508e78'
Old sha256sum was: 'd0d8475574daec0ce4cf2dfd8b183801ae771495946c7092a4af561a50f794d7'
New sha256sum is : 'b8959dc99f1f6a15f5d37f3d26e31fe40a2b2c40392da59aaf569fd056628d45'

--
2025 Mar 24 11:15:38 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/socfortress-security.conf' modified
Mode: realtime
Changed attributes: permission
Permissions changed from 'rw-r--r--' to 'rwxrwxrwx'

Attributes:
 - Size: 133
 - Permissions: rwxrwxrwx
 - Date: Mon Mar 24 11:15:34 2025
 - Inode: 1050937
 - User: root (0)
--
2025 Mar 24 11:15:43 (xiegd-PC) any->syscheck
Rule: 553 (level 7) -> 'File deleted.'
File '/etc/socfortress-security.conf' deleted
Mode: realtime

Attributes:
 - Size: 133
 - Permissions: rwxrwxrwx
 - Date: Mon Mar 24 11:15:34 2025
 - Inode: 1050937
 - User: root (0)
 - Group: root (0)
 - MD5: 10f8ce1a4e12b3cff6df66906ef045d6
--
2025 Mar 24 11:16:00 (xiegd-PC) any->syscheck
Rule: 554 (level 5) -> 'File added to the system.'
File '/etc/sudoers.d/socfortress_test' added
Mode: realtime

Attributes:
 - Size: 0
 - Permissions: rw-r--r--
 - Date: Mon Mar 24 11:16:01 2025
 - Inode: 1050937
 - User: root (0)
 - Group: root (0)
 - MD5: d41d8cd98f00b204e9800998ecf8427e
--
2025 Mar 24 11:16:01 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/sudoers.d/socfortress_test' modified
Mode: realtime
Changed attributes: size,md5,sha1,sha256
Size changed from '0' to '62'
Old md5sum was: 'd41d8cd98f00b204e9800998ecf8427e'
New md5sum is : 'b128398cb74acd861636eb75a860bef2'
Old sha1sum was: 'da39a3ee5e6b4b0d3255bfef95601890afd80709'
New sha1sum is : '5e7d7cca782ec0a1ef20550262860dec03488678'
Old sha256sum was: 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'
New sha256sum is : 'eed0f720b483e6323797fa311a539081bb1adc95f8c34a392a7b702df7885136'

--
2025 Mar 24 11:16:06 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/sudoers.d/socfortress_test' modified
Mode: realtime
Changed attributes: size,mtime,md5,sha1,sha256
Size changed from '62' to '95'
Old modification time was: '1742786161', now it is '1742786166'
Old md5sum was: 'b128398cb74acd861636eb75a860bef2'
New md5sum is : '0121ef8a7105e02c7012ac4fc4d455c2'
Old sha1sum was: '5e7d7cca782ec0a1ef20550262860dec03488678'
New sha1sum is : 'c9a020ab4bd73052be746873909c944029dda44f'
Old sha256sum was: 'eed0f720b483e6323797fa311a539081bb1adc95f8c34a392a7b702df7885136'
New sha256sum is : '8fd826aaf747b04035952a61098c43df893d8aa3bb5d1134b7a5f79064ed507f'
--
2025 Mar 24 11:16:11 (xiegd-PC) any->syscheck
Rule: 550 (level 7) -> 'Integrity checksum changed.'
File '/etc/sudoers.d/socfortress_test' modified
Mode: realtime
Changed attributes: permission
Permissions changed from 'rw-r--r--' to 'rwxrwxrwx'

Attributes:
 - Size: 95
 - Permissions: rwxrwxrwx
 - Date: Mon Mar 24 11:16:06 2025
 - Inode: 1050937
 - User: root (0)
--
2025 Mar 24 11:16:16 (xiegd-PC) any->syscheck
Rule: 553 (level 7) -> 'File deleted.'
File '/etc/sudoers.d/socfortress_test' deleted
Mode: realtime

Attributes:
 - Size: 95
 - Permissions: rwxrwxrwx
 - Date: Mon Mar 24 11:16:06 2025
 - Inode: 1050937
 - User: root (0)
 - Group: root (0)
 - MD5: 0121ef8a7105e02c7012ac4fc4d455c2


```

```xml
<group name="socfortress,">
  <rule id="200980" level="1">
    <field name="integration">custom-socfortress</field>
    <description>SOCFortress IoC</description>
    <options>no_full_log</options>
  </rule>
  <rule id="200981" level="1">
    <if_sid>200980</if_sid>
    <field name="socfortress.status_code">^404$</field>
    <description>No matching IoC Detected</description>
    <options>no_full_log</options>
  </rule>
  <!-- 其他子规则... -->
</group>
```

基础规则 ID 200980 依赖于 `<field name="integration">`custom-socfortress `</field>`

* 这意味着规则只会在集成了名为"custom-socfortress"的集成模块时触发
* 您的系统中没有配置此集成，因此基础规则永远不会触发
* 所有子规则（200981、200982等）使用 if_sid 依赖于基础规则，形成了级联失效

## 三. 演示框架设计

| Wazuh功能模块           | 对应攻击场景 | 演示操作                       | Dashboard展示重点                                                  |
| ----------------------- | ------------ | ------------------------------ | ------------------------------------------------------------------ |
| Security Events         | 网络层攻击   | 端口扫描、异常连接             | Security Events标签页，关注Network related事件                     |
| Integrity Monitoring    | 文件监控测试 | 关键文件修改、敏感目录创建文件 | Integrity Monitoring标签页，FIM告警                                |
| System Auditing         | 主机安全测试 | 执行敏感命令、权限提升         | Security Events标签页，Command execution与Privilege escalation事件 |
| Vulnerability Detection | 漏洞利用测试 | 利用已知CVE                    | Vulnerability标签页，显示已知漏洞                                  |
| Malware Detection       | 恶意软件测试 | EICAR测试、反弹Shell           | Security Events标签页，Malware相关事件                             |

### 3.1 信创终端办公场景安全威胁分析与防御优先级建议

#### 3.1.1 一线威胁（高频高危）

1.钓鱼攻击与社会工程学

- 钓鱼邮件附件/链接
- 办公文档中的恶意宏
- 假冒软件更新通知

2.针对性社会工程学攻击

- 恶意软件
- 木马后门程序（尤其是针对信创环境定制的变种）
- 勒索软件（针对办公文档的加密）
- 信息窃取类恶意软件（针对敏感数据）

3.远程控制与后门

- RAT（远程访问木马）
- 持久化后门
- 合法工具的恶意使用（如远程管理工具）

#### 3.1.2 次级威胁

1.本地特权提升

- 系统漏洞利用获取更高权限
- 配置错误利用

```
# 1. 创建关键文件修改(FIM测试)
echo "WAZUH TEST FILE - DELETE ME" | sudo tee /etc/wazuh-test.conf
sleep 5
sudo chmod 777 /etc/wazuh-test.conf
sleep 5
sudo rm -f /etc/wazuh-test.conf

# 2. 生成失败登录(安全事件测试)
for i in {1..5}; do
  su fakeuser -c "echo wrongpassword" 2>/dev/null
  sleep 1
done

# 3. 创建特权测试文件
sudo touch /etc/sudoers.d/test_wazuh
sleep 3
sudo rm -f /etc/sudoers.d/test_wazuh

# 4. 搜索SUID文件(可疑命令)
sudo find / -perm -4000 -type f 2>/dev/null | head -5 > /tmp/suid_files.txt
```

> UOS Agent端

![1742453810473](image/信创主机进阶方案/1742453810473.png)

> Wazuh 服务器后台

```
从文件完整性监控功能看：
1. Wazuh FIM模块成功检测到了agent上的文件变化；
2. 监控范围包括关键系统目录(/etc)和用户目录(/home)；
3. 事件类型覆盖了added、modified和deleted三种操作，分别对应规则ID554、550、557，added操作安全级别为5，修改和删除操作为7；
4. 安全级别根据文件敏感性分配为5(普通)和7(重要)；
5. 日志包含了完整的时间戳、文件路径和操作描述；
```

![1742454995993](image/信创主机进阶方案/1742454995993.png)

![1742455019363](image/信创主机进阶方案/1742455019363.png)

![1742455547681](image/信创主机进阶方案/1742455547681.png)

![1742455854115](image/信创主机进阶方案/1742455854115.png)

### 3.2 内部横向移动

- 从一个终端扩散到其他终端
- 内网信息收集

### 3.网络攻击

- 中间人攻击
- 会话劫持
- Wi-Fi安全问题

### 4.供应链攻击

- 通过信创生态供应链投放恶意代码

## 四. wazuh事件处理完整流程

Wazuh的事件处理遵循以下严格顺序的流程：

1. 事件产生：系统操作产生原始日志（如auth.log记录SSH失败尝试）
2. Agent收集：Wazuh Agent监控并读取这些日志文件
3. 事件解码：Agent将原始日志解析为结构化数据
4. 事件队列：解析后的事件存储在/var/ossec/queue/ossec目录中
5. 事件传输：Agent将队列中的事件发送至Manager
6. 规则匹配：Manager根据预定义规则分析事件
7. 告警生成：匹配成功的事件生成相应级别的告警
