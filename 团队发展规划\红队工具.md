# Red Team Attack

### **红队武器库**

> 准备阶段

- [攻击队反溯源准则](https://github.com/qingluoyu/Pentest_baseline/blob/master/%E6%94%BB%E5%87%BB%E9%98%9F%E5%8F%8D%E6%BA%AF%E6%BA%90%E5%87%86%E5%88%99.md)
- [红队标准手册](https://github.com/Harveysn0w/Redteam_Standard)
- [红/蓝队环境自动化部署工具](https://github.com/ffffffff0x/f8x)
- [红队基础设施自动化部署工具](https://github.com/QAX-A-Team/LuWu)

> 信息收集类
* [Nmap 好用的端口扫描工具](https://nmap.org/)
* [Masscan-超快的端口扫描工具](https://github.com/robertdavidgraham/masscan)
* [TXPortMap-在速度与准确度之间寻找一个平衡的端口扫描工具](https://github.com/4dogs-cn/TXPortMap)
* [OneForAll是一款功能强大的子域收集工具](https://github.com/shmilylty/OneForAll)
* [httpx-一个快速且多用途的 HTTP 工具包](https://github.com/projectdiscovery/httpx)
* [AppInfoScanner 适用于红队的移动端(Android、iOS、WEB、H5、静态网站)信息收集工具](https://github.com/kelvinBen/AppInfoScanner)
* [GitDorker Python程序，使用Github dorks从Github搜索敏感信息](https://github.com/obheda12/GitDorker)
* [dirsearch - Web path scanner 目录扫描工具](https://github.com/maurosoria/dirsearch)
* [ffuf 用Go编写的模糊测试工具](https://github.com/ffuf/ffuf)
* [JSFinder-一款用作快速在网站的js文件中提取URL，子域名的工具](https://github.com/Threezh1/JSFinder)
* [Finger-一款红队在大量的资产中存活探测与重点攻击系统指纹探测工具](https://github.com/EASY233/Finger)
* [Railgun是一款GUI界面的渗透工具，综合类的扫描工具](https://github.com/lz520520/railgun)
* [EHole(棱洞)-红队重点攻击系统指纹探测工具](https://github.com/EdgeSecurityTeam/EHole)
* [ESTeye(棱眼)-红蓝攻防中快速定位目标的真实资产](https://github.com/EdgeSecurityTeam/ESTeye)
* [ServerScan一款使用Golang开发的高并发网络扫描、服务探测工具](https://github.com/Adminisme/ServerScan)
* [DarkEye 渗透测试情报收集工具](https://github.com/zsdevX/DarkEye)
* [sherlock - 通过社交网络上的用户名搜寻社交媒体帐户](https://github.com/sherlock-project/sherlock)
*  [用Rust编写的快速，简单，递归的内容发现工具](https://github.com/epi052/feroxbuster)

> 漏洞扫描\安全评估类

* [一款功能强大的安全评估工具 - Xray](https://github.com/chaitin/xray)
* [pocsuite3 开源的远程漏洞测试框架](https://github.com/knownsec/pocsuite3)
* [网络安全测试工具 - Goby](https://github.com/gobysec/Goby)
* [Myscan 被动扫描器](https://github.com/amcai/myscan)
* [kunpeng是一个Golang编写的开源POC框架/库](https://github.com/opensec-cn/kunpeng)
*  [pocscan PoC扫描器 主要用于指纹识别后，进行漏洞精准扫描](https://github.com/DSO-Lab/pocscan)

> 漏洞利用\WebShell\网站管理\后渗透类

*  [Metasploit Framework](https://github.com/rapid7/metasploit-framework)
*  [Mimikatz Windows 密码抓取神器](https://github.com/gentilkiwi/mimikatz)
*  [“冰蝎”动态二进制加密网站管理客户端](https://github.com/rebeyond/Behinder)
*  [Cknife 中国菜刀](https://github.com/Chora10/Cknife)
*  [基于 poweshell 的命令执行框架 后渗透利器](https://github.com/EmpireProject/Empire)
*  [中国蚁剑是一款跨平台的开源网站管理工具](https://github.com/2Quico/antSword)
*  [Godzilla 哥斯拉](https://github.com/BeichenDream/Godzilla)
*  [网络测试中的瑞士军刀，包含 impacket、PowerSploit 等多种模块](https://github.com/byt3bl33d3r/CrackMapExec)
*  [windows-kernel-exploits Windows平台提权漏洞集合](https://github.com/SecWiki/windows-kernel-exploits)
*  [linux-kernel-exploits Linux平台提权漏洞集合](https://github.com/SecWiki/linux-kernel-exploits)
*  [BurpSuite 一个Web应用程序集成攻击平台](https://down.52pojie.cn/?query=burpsuite)
*  [各种类型的Webshel​​l 后门收藏](https://github.com/xl7dev/WebShell)

> 脚本聚合类

* [Bug Bounty Tools (一些实用的赏金Python脚本)](https://github.com/m4ll0k/Bug-Bounty-Toolz)
* [FofaSpider Fofa爬虫支持高级查询语句批量爬取](https://github.com/KpLi0rn/FofaSpider)
*  [fofa2Xray 一款联合fofa与xray的自动化批量扫描工具](https://github.com/piaolin/fofa2Xray)
*  [SScan 一款src捡洞扫描器](https://github.com/yhy0/SScan)
*  [hscan 集成crawlergo、xray、dirsearch、nmap等工具的src挖掘工具](https://github.com/zongdeiqianxing/hscan)

### **红队漏洞库**

> 历史漏洞库,最新漏洞PoC/Exp,快速检索。

* [PeiQi文库](http://wiki.peiqi.tech/)
* [pwnwiki](https://www.pwnwiki.org/)
* [棱角社区对外进行公布一些最新漏洞](https://github.com/EdgeSecurityTeam/Vulnerability)
* [Exploit-DB](https://www.exploit-db.com/)
* [知道创宇 Seebug](https://www.seebug.org/)
* [WooYun 历史漏洞](http://wy.zone.ci)
* [Metasploit 利用模块](https://www.rapid7.com/db/)


### **红队资料库**

> 奇淫技巧,文库资料,来这里找。

* [Book of BugBounty Tips](https://gowsundar.gitbook.io/book-of-bugbounty-tips/)
* [Red Team Tips](https://vincentyiu.com/red-team-tips/)
* [Awesome-Red-Teaming 一些优秀的红队资料](https://github.com/yeyintminthuhtut/Awesome-Red-Teaming)
* [应用程序的一些安全备忘](https://0xn3va.gitbook.io/cheat-sheets/)
* [狼组公开知识库](https://wiki.wgpsec.org/knowledge/)
* [知道创宇 Paper](https://paper.seebug.org/)
* [先知技术社区](https://xz.aliyun.com/)
* [红队中易被攻击的一些重点系统漏洞整理](https://github.com/r0eXpeR/redteam_vul)
* [黑客，渗透测试者和安全研究人员的各种出色列表的集合](https://github.com/Hack-with-Github/Awesome-Hacking)
* [实用的一些Payload和一些bypass技巧](https://github.com/swisskyrepo/PayloadsAllTheThings)
* [KingOfBugBountyTips](https://github.com/KingOfBugbounty/KingOfBugBountyTips)
* [优秀的安全编排、自动化和响应(SOAR)清单](https://github.com/correlatedsecurity/Awesome-SOAR)
* [红蓝对抗及护网资料分享](http://hackdig.com/10/hack-181850.htm)
*  [收集的一些有质量安全文章](https://github.com/tom0li/collection-document)
*  [Red-Team-Infrastructure-Wiki 红队基础建设WIKI](https://github.com/bluscreenofjeff/Red-Team-Infrastructure-Wiki)
*  [一些赏金技巧、知识和一些脚本](https://six2dez.gitbook.io/pentest-book/)
*  [Purple Teaming 一些红队相关的技术分享](https://www.reddit.com/r/purpleteamsec/)
*  [MSSQL注入提权,bypass的一些总结](https://github.com/aleenzz/MSSQL_SQL_BYPASS_WIKI)
*  [redteam-notebook 红队笔记](https://github.com/foobarto/redteam-notebook)
*  [关于红队方面的学习资料](https://github.com/zhaoweiho/redteam-tips)


### **在线工具库**

> 实用的在线工具推荐。

* [FOFA.so](https://fofa.so/)
* [企查查](https://www.qcc.com/)
* [百度爱企查(免费)](https://aiqicha.baidu.com/)
* [IPIP.NET](https://www.ipip.net/)
* [CMD5 在线解密](https://www.cmd5.com/)
* [SOMD5 在线解密](https://www.somd5.com/)
* [CTF 在线工具箱](http://ctf.ssleye.com/)
* [Windows 提权辅助工具](https://i.hacking8.com/tiquan/)
* [Windows杀软在线对比辅助](https://maikefee.com/av_list)

