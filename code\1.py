from openai import OpenAI

client = OpenAI(
    base_url="https://gemi2api-server-i72o.onrender.com/v1",
    api_key="test"
)

for model in [
    "gemini-2.0-flash",
    "gemini-2.5-pro",
]:
    resp = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": "一句话点评 Python 语言"}],
        stream=False
    )
    print(model, "=>", resp.choices[0].message.content)
