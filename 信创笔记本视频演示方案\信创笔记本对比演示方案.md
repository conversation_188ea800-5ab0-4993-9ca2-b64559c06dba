# 信创笔记本与Windows笔记本安全防护对比演示方案

**攻击载体**：伪装PDF的木马文件（SHA256: xxxx），通过钓鱼邮件传播
**演示目标**：展现UOS系统硬件级安全防护体系与Windows系统防御机制的本质差异
**攻击一致性验证**：两设备均从同一QQ邮箱账户下载样本

---

## 一、攻击注入阶段对比

| 维度                   | 信创笔记本（UOS）                                    | Windows 11 联想笔记本  |
| ---------------------- | ---------------------------------------------------- | ---------------------- |
| **文件存储验证** | 硬件TCM芯片实时验证文件数字签名，标记未签名高危文件  | 无文件完整性校验机制   |
| **用户交互设计** | 强制弹窗提示"高风险文件类型不匹配"（.exe伪装为.pdf） | 仅显示常规下载完成提示 |

---

## 二、恶意代码执行阶段响应对比

### 1. 检测机制深度对比

| 检测层级                                                                                                                                                                                                         | 信创笔记本技术实现                                                 | Windows防御缺口                            |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ | ------------------------------------------ |
| **硬件层**                                                                                                                                                                                                 | TCM安全芯片实时监控DMA访问，阻断异常内存写入                       | 无硬件级内存保护机制                       |
| **内核层**                                                                                                                                                                                                 | 定制化安全内核拦截非法系统调用（strace日志显示阻断8次危险syscall） | 系统API调用无深度监控                      |
| **应用层**                                                                                                                                                                                                 | AppArmor强制访问控制策略阻止explorer.exe执行下载操作               | 无强制访问控制，explorer.exe拥有系统级权限 |
| **网络层**                                                                                                                                                                                                 | 基于DPDK的流量清洗系统阻断4444端口C2通信（tshark捕获0次成功连接）  | 防火墙未识别加密C2流量                     |
| 具体的UOS检测阶段如下，即通过监控各种系统层面（日志、进程、网络、资源、文件）的指标，发现潜在的威胁信号，并将这些信号汇总，为后续的分析阶段提供关键线索，例如可疑进程PID、暴力破解IP、恶意文件路径和C2连接信息。 |                                                                    |                                            |

* **系统概览和环境监控：**  首先收集系统的基本信息（操作系统、用户、负载、AppArmor状态），为后续分析提供上下文。同时监控CPU、内存和网络连接，以便发现异常资源占用和可疑连接。
* **日志分析：**  通过分析系统日志（`journalctl`），特别是关注SSH登录失败事件，以识别潜在的暴力破解尝试。这有助于发现外部入侵的初步迹象。
* **进程行为监控：** 使用 `strace` 监控特定进程（例如PID 6888）的行为，追踪其执行的系统调用。这能揭示进程是否正在进行恶意操作，例如执行下载命令或建立异常网络连接。
* **网络活动捕获：** 利用 `tshark` 捕获网络流量，特别是关注特定端口（例如4444）。这有助于检测与恶意服务器的通信，尤其是加密的C2（命令与控制）通信。
* **资源使用监控：** 使用 `psutil` 监控进程的资源占用情况，发现异常高CPU或内存占用的进程，这可能是恶意软件活动的迹象。
* **安全机制检查：** 检查UOS安全机制的状态，例如AppArmor的状态，发现潜在的安全策略配置问题（例如explorer.exe处于complain模式），这可能被攻击者利用。
* **文件系统扫描：** 使用 `clamav` 等工具扫描文件系统，查找已知的恶意文件。同时，它还会进行启发式分析（例如检查文件熵值）来识别潜在的未知恶意文件。对于可疑文件，还会分析其类型和内嵌内容（例如HTML中的JS代码）。、
  ![1740558110026](image/信创笔记本对比演示方案/1740558110026.png)

### 2. 威胁分析能力对比

| 分析维度               | 信创笔记本取证深度                                                                                                       | Windows局限性                  |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------ | ------------------------------ |
| **内存取证**     | 通过gcore获取完整进程内存镜像，volatility3分析发现3处代码注入痕迹                                                        | 无原生内存取证工具             |
| **文件熵值分析** | 检测到伪装PDF熵值7.8（超过可执行文件阈值6.2），触发启发式告警                                                            | 未实施文件结构深度分析         |
| **行为建模**     | 建立进程行为基线，检测到异常子进程创建，具体为HTTPS C2通信，TLS 1.3加密流量被硬件SSL代理解密，识别出CN=malicious-c2-cert | 依赖静态特征库，无动态行为分析 |

具体的分析阶段如下，即对检测到的异常进行确认和定性的过程。通过内存取证确认注入行为，通过静态分析揭示恶意文件的内部特征，通过威胁情报关联确认其恶意性并了解其可能的来源和行为模式。最终得出明确的分析结论，判断威胁的严重程度，并为清理阶段提供具体的行动指导。

* **内存取证：** 通过转储可疑进程的内存（`gcore`），并使用 `volatility3` 等内存取证工具进行分析，查找进程注入的痕迹。例如，发现恶意模块被注入到其他进程中。
* **静态分析：** 对可疑文件（例如 `unknown.exe`）进行静态分析，使用 `binwalk`、`objdump` 和 `strings` 等工具检查文件的结构、头部、段信息、字符串等，以发现异常的入口点、混淆的代码、以及可能暴露恶意目的的字符串（例如C2通信相关的字符串）。
* **威胁情报关联：**  将检测到的恶意文件哈希值等信息提交到威胁情报平台（例如VirusTotal），获取已知的威胁信息，例如检测率、所属家族、相关的IOCs（入侵指标）以及与MITRE ATT&CK框架的关联。
  ![1740558091816](image/信创笔记本对比演示方案/1740558091816.png)

---

## 三、威胁处置阶段对比

### 1. 自动化响应能力

| 处置动作           | 信创笔记本响应策略                                                | Windows处理方式              |
| ------------------ | ----------------------------------------------------------------- | ---------------------------- |
| **进程终止** | 联动硬件监控模块发送SIGKILL信号，确保内核级清除（响应时间<200ms） | 需手动终止进程，存在残留风险 |
| **文件清除** | 实施3次DoD 5220.22-M标准擦除，记录文件哈希至区块链存证            | 简单删除可被数据恢复         |
| **网络隔离** | 自动生成nftables规则阻断/24网段，持续72小时动态封锁               | 需手动配置防火墙规则         |

### 2. 系统加固措施

| 加固维度                                                                                                                                                                                           | 信创笔记本防护升级                                             | Windows缺陷            |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------- | ---------------------- |
| **策略优化**                                                                                                                                                                                 | 自动切换AppArmor至enforce模式，生成定制化应用白名单            | 组策略更新需管理员介入 |
| **漏洞修复**                                                                                                                                                                                 | 基于ATT&CK框架生成攻击路径图，自动修补3处潜在利用点            | 依赖月度补丁周期       |
| **溯源能力**                                                                                                                                                                                 | 生成完整攻击链报告（含MITRE TTPs映射），定位初始钓鱼邮件发信IP | 日志分散缺乏关联分析   |
| 具体的清理阶段如下，即对威胁进行有效移除和系统加固的过程。通过终止进程、安全删除文件、清理持久化入口、阻断网络连接以及修复安全策略，彻底消除威胁的影响，并提升系统的安全性，防止同类攻击再次发生。 |                                                                |                        |

* **进程终止：** 使用 `pkill` 命令，通过不同的信号（SIGTERM和SIGKILL）尝试终止恶意进程，确保其无法继续活动。
* **文件安全擦除：** 使用 `shred` 命令，按照安全标准（例如DoD 5220.22-M）多次覆盖并擦除恶意文件，确保其无法被恢复。同时，删除伪装的HTML文件，并记录其哈希值。
* **持久化清理：**  清理恶意软件可能设置的持久化机制，例如禁用和移除相关的 `systemd` 服务，删除 `cron` 任务和自启动项，防止恶意软件在系统重启后再次运行。
* **网络阻断：** 使用 `nftables` 防火墙添加规则，阻止与恶意C2服务器的通信，切断恶意软件与外部的连接。
* **AppArmor策略修复：**  修复之前发现的AppArmor策略问题，将处于 `complain` 模式的策略切换到 `enforce` 模式，以增强系统的安全防护能力，防止再次被注入。
  ![1740558119429](image/信创笔记本对比演示方案/1740558119429.png)

---

## 四、可视化演示设计建议

1. **实时对比演示**

   - 左屏投射信创笔记本：
     * 利用终端实时可视化攻击路径阻断
   - 右屏投射Windows笔记本：
     * 录制视频循环播放典型攻击表现（鼠标劫持、弹窗、信息窃取等）
2. **攻击时间轴标注**

   ```markdown
   00:00 木马下载触发 → UOS弹出数字签名告警 / Windows正常下载
   00:05 文件执行尝试 → UOS硬件芯片阻断写入 / Windows开始释放恶意负载
   00:15 网络连接建立 → UOS流量清洗系统拦截 / Windows建立C2通道
   00:30 系统影响呈现 → UOS启动自动化处置 / Windows完全失控
   ```
3. **取证数据展示**

   - 陈列UOS生成的JSON格式安全报告（含IOC指标、ATT&CK映射）
   - 对比Windows事件查看器碎片化日志

---

## 五、技术优势总结

1. **纵深防御体系**：实现硬件TCM芯片→安全内核→应用沙箱的三层防护
2. **主动诱捕能力**：通过高交互式蜜罐系统捕获0day攻击特征
3. **闭环处置机制**：从检测到加固的完整自动化响应链（MTTD<15s, MTTR<2min）
4. **合规性保障**：满足等保2.0三级要求中7项关键控制点

---

**风险提示**：实际演示需确保测试环境隔离（建议采用 Faraday cage），避免恶意代码意外传播。

## Tips：流畅度演示方案如下：

- 网页浏览器：Chrome/360安全浏览器 18-22个标签

  - 含：3个视频直播页 + 5个图文长文页 + 10个常规资讯页
- 办公文档：

  - WPS：3个PPT（50+页含动画） + 2个Word（50页带图表） + 1个Excel（万行数据）
  - PDF阅读器：4个技术手册（单文件30MB以上）
- 常驻软件：即时通讯 + 邮件客户端 + 安全卫士

---

## 第一阶段：检测（Detection Phase）

在系统安全监控的日常巡检中，实时进程监控和资源占用监控是关键的一环，一旦出现异常的CPU或内存占用暴增、可疑的网络连接行为，系统将自动触发警示并进行初步检测。

**脚本文案示例：**

> **系统通过实时进程和网络监控，识别到潜在的木马行为：**
> ① 发现一个伪装成系统进程的未知程序（如 `unknown.exe`），CPU及内存占用率短时间内显著上涨
> ② 捕捉到频繁连接境外IP `************` 的 4444 端口
> ③ 进一步分析确认，该IP端口正是黑客的C2（命令与控制）服务器
>
> **一旦通信隧道建立，攻击者将获得：**
>
> - **远程命令执行**（可直接对目标系统下达任意指令）
> - **屏幕监控**（实时查看或录制受害终端的画面）
> - **文件窃取**（上传或下载关键数据文件）
>
> **初步判断：**
>
> - 该恶意程序通过伪装成合法进程，试图在系统中常驻运行
> - 网络流量中存在持续的加密心跳包，表明远程C2处于“活跃控制”状态
> - 与常规病毒相比，其具备更强的隐匿性和破坏性，可能属于高级木马或APT攻击工具链的一部分
>
> **系统将在下一步进入「分析阶段 (Analysis)」，提取并审查可疑进程和文件，以确认攻击者的具体手段和目标：**

## 第二阶段：分析（Analysis Phase）

在完成了对恶意程序的初步检测后，系统会进入深入分析阶段，主要针对可疑进程和文件的行为、内存取证以及威胁情报进行综合判断，从而确定该木马的威胁等级和攻击手段。

**脚本文案示例：**

> **系统在内存层面进一步取证，试图确认恶意程序的真实意图：**
> ① 提取可疑进程（如PID=6888）的内存镜像，搜索异常注入痕迹
> ② 识别到恶意动态链接库 (malicious.so) 被加载到合法进程 (explorer.exe)
> ③ 借助静态分析工具，对unknown.exe进行反汇编和字符串检索
> ④ 发现硬编码的AES加密密钥及“C2_Heartbeat”等关键字
>
> **结合威胁情报(例如VirusTotal/ATT&CK矩阵)：**
>
> - 检测率高：该样本在主流杀毒引擎中被判定为「高危木马」
> - 攻击者常用手段：进程注入、通信加密、伪装合法文件
> - C2地址 (************) 已被安全社区多次标记为APT关联IP
>
> **综合研判后得出：**
> ✔️ 这是一个具备远程控制、文件窃取和持久化能力的高级木马
> ✔️ 其利用PDF伪装诱导点击，以绕过常规防护
> ✔️ 经过内存和静态分析，确认木马家族与APT29的加密手段相似
>
> **下一步系统将进入“清理阶段”，彻底终止并移除该木马：**

---

## 第三阶段：清理（Remediation Phase）

在分析确认威胁后，系统开始对目标主机进行深度清理，包括终止恶意进程、删除木马文件、切断外联通道、修复安全策略等，保证系统最终恢复到安全状态。

**脚本文案示例：**

> **系统执行多重清理措施，以防止木马残留：**
> ① 进程层面：强制结束unknown.exe及其注入进程，确保无存活的恶意进程
> ② 文件层面：安全擦除恶意文件 (shred算法)、删除伪装HTML (XX单位2024年干部任命文件.pdf.html)
> ③ 持久化入口：禁用可疑的systemd服务、移除cron自启动任务、删除~/.config/autostart/unknown.desktop 等
> ④ 网络层面：通过nftables或防火墙策略，封禁与境外C2地址 (************:4444) 的所有出站连接
> ⑤ 加固策略：将explorer.exe等关键进程切换到AppArmor enforce模式，防止重复被注入
>
> **最终验证及总结：**
> ✔️ 木马相关进程彻底终止，文件已安全擦除
> ✔️ 网络出口已切断C2连接，防止信息再度泄露
> ✔️ 计划重启系统，以应用所有安全策略并完成修复
> ✔️ 清理过程中产出的日志与取证数据，将留存备查与后续追溯
>
> **至此，系统清理阶段结束，主机恢复安全状态。**

---

1.流畅度演示部分，需要去掉口语化部分，使其更专业一些
2.信创防御演示时，去掉“windows”字眼，因为本来是要进行信创产品之间的对比的。
3.在流畅度演示的时候，第一点进行CPU多级缓存技术，以及固态硬盘（随便提一下，毕竟不是核心技术）；第二点为针对信创的芯片驱动优化。
4.做一些炫酷且专业的视频开头和结尾（10s左右），最好涉及AI相关的，可以用AI视频生成，可以涉及到我们研究院的logo。
5.需要发邮件时的真实场景，如把三台电脑放在隔壁会议室（第三台电脑是用来发送邮件的），要涉及到整体的三体机器远景和发邮件时的近景。

---

<开头特效：三维粒子汇聚形成产品矩阵>

> "制作视频开头特效，通过三维粒子效果，粒子从四面八方汇聚，逐渐形成一个产品矩阵。整个过程粒子流动顺畅，动感十足，背景色调与我们研究院的Logo相匹配，产生一种科技感与未来感的氛围。最终，产品矩阵的形态稳定，逐渐展示出研究院的Logo，平滑过渡至视频的主内容部分。"

西交网络空间安全研究院于2024年12月份推出了“暨安”品牌安全计算终端系列产品，包括安全信创体系计算机和安全通用体系计算机，台式机和笔记本电脑均已完成质量测试和安全功能验证，进入产业化推广阶段。

研究院自主研制的安全信创计算终端产品从性能体验和流畅度等方面超过市场同配置产品，增加了网络安全管控和日志审计等多项功能，可以使办公系统更加安全，充分抵御入侵。在同等配置条件下，售价不高于市场同类产品，研究院在诸暨组建产品售后技术支持服务团队，确保运维和售后服务相较传统品牌有充分优势。

“暨安”系列产品在解决信创计算机性能方面的核心技术主要包括以下几个方面。

###### 一，相较于其他信创笔记本流畅度的提升

**1. 存储与缓存架构创新**► **多级 CPU 缓存技术**搭载自主研发智能缓存控制器，构建 L1/L2/L3 三级异构缓存架构：

- L1 指令缓存采用分支预测 + 指令预取技术，提高命中率
- L2 数据缓存实现动态容量分配
- L3 共享缓存支持 NUMA-aware 访问优化

► **SSD 全栈加速方案**基于固态优化技术，确保办公场景数据可靠性及续航能力：

- PCIe 4.0 接口实现 GB/S级顺序读取
- 4K 随机读写速度提升
- 内置国密 SM4/SM3 硬件加密引擎，加密损耗降低

**2. 芯片驱动架构级优化

基于芯片指令集特性，构建从固件层到应用层的全栈驱动优化体系：

**► 驱动架构创新**

- 采用**分层模块化驱动架构（LMDA）**，实现硬件抽象层（HAL）、内核驱动层（KMD）、用户态驱动层（UMD）三级解耦
- 关键路径驱动实现**热补丁动态加载机制**，支持驱动模块更新免系统重启
- 开发**硬件资源虚拟化驱动框架**，支持 CPU/GPU/NPU 资源按需动态分配

**► 驱动性能突破**

- **中断响应优化**：通过 MSI-X 中断映射算法改进
- **DMA 引擎增强**：实现 DMA 零拷贝驱动，内存带宽利用率提升
- **智能功耗调节**：驱动级实现 DVFS 3.0 动态调频，配合负载预测模型，使整机续航得到提升

**► 驱动安全加固**

- 在驱动层集成**国密 SM2/SM4 硬件加速引擎**，加密吞吐量达 40Gbps
- 实现**驱动完整性度量**，采用 TCM 2.0 可信计算模块对驱动进行启动时验证 + 运行时监控
- 构建**驱动漏洞熔断机制**，对异常 API 调用实施毫秒级隔离

###### 二，信创防御演示

> [!NOTE]
> 加入三台笔记本画面，即第三台笔记本进行邮件的发送，进行对比的电脑进行接收。画面要涉及到整体的三体机器远景和发邮件时的近景特写。```
> ! 实验组：暨安笔记本A（发送端）
> ! 对照组：系统设备B（接收端）
> ! 监控组：暨安笔记本C（安全审计端）

本节展示信创笔记本对网络攻击的防御能力。通过对比测试，Windows系统在打开恶意PDF文件后出现以下安全隐患：系统弹出大量恶意弹窗，木马程序成功植入，导致鼠标控制权限被劫持，系统功能严重受损。

信创系统在相同攻击场景下，依托三层防御机制成功拦截威胁：

1. **检测阶段**：系统实时监控进程活动和资源占用率，当检测到异常的CPU使用率、内存占用或可疑网络通信时，自动触发预警机制并启动初步检测。
2. **分析阶段**：对可疑进程行为、文件特征、内存矩阵及威胁情报进行深度分析，评估木马威胁等级和攻击特征。
3. **清理阶段**：执行进程终止、恶意文件清除、网络通道阻断等安全处置，同时修复受损安全策略，确保系统恢复至安全状态。

以上为信创笔记本安全防御能力演示内容。

[结尾特效：三维产品渲染 + 认证标识矩阵]

> "制作视频结尾特效，首先展示一个三维旋转的产品渲染模型，产品逐渐放大并旋转，突出其精密度与高科技感。接着，在产品周围出现认证标识矩阵，这些标识整齐排列，渐变呈现，突出安全性与权威性。最后，整个场景逐渐消失或淡出，展示研究院的Logo，并添加‘感谢观看’字样，营造出专业和可信的氛围。"
