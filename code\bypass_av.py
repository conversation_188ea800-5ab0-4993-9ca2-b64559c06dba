import base64
import sys
import time
from random import randint
import ctypes

def xor_encrypt(data: bytes, key: int) -> bytes:
    """简单的 XOR 加密"""
    return bytes(b ^ key for b in data)

def get_random_sleep():
    """随机休眠以避免沙箱检测"""
    time.sleep(randint(1, 3))

def check_debugger():
    """检测是否在调试器中运行"""
    try:
        return ctypes.windll.kernel32.IsDebuggerPresent()
    except:
        return False

def main():
    # 基本的反调试
    if check_debugger():
        sys.exit(0)
    
    # 随机延迟执行
    get_random_sleep()
    
    # 这里仅打印消息作为演示
    print("这是一个概念验证程序")
    print("实际场景中,这里可以是任何合法的程序逻辑")

if __name__ == "__main__":
    main() 