# 近源攻击

### ▶ 刘海博：

| 时间                | 任务名称                                   | 本周进展                                                                                                            | 下周计划                                             | 备注 |
| ------------------- | ------------------------------------------ | ------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------- | ---- |
| **2025.2.28** | 安排CS结合免杀工具，进行木马免杀，内网渗透 | 1.学习内网横向移动的相关知识，如IPC横向，WMI横向，smb横向。<br />2.了解病毒免杀的方式并学习，查找相关病毒加壳工具。 | 1.继续学习内网横向移动知识。<br />2.病毒免杀的学习。 |      |
|                     |                                            |                                                                                                                     |                                                      |      |
|                     |                                            |                                                                                                                     |                                                      |      |
|                     |                                            |                                                                                                                     |                                                      |      |
|                     |                                            |                                                                                                                     |                                                      |      |
|                     |                                            |                                                                                                                     |                                                      |      |
|                     |                                            |                                                                                                                     |                                                      |      |

### ▶ 卫海波

| 时间                | 任务名称                          | 本周进展                                           | 下周计划                                 | 备注 |
| ------------------- | --------------------------------- | -------------------------------------------------- | ---------------------------------------- | ---- |
| **2025.2.28** | 研究CS结合Mimikatz的shellcode免杀 | 1.了解木马免杀掌握方向<br />2.学习简易木马相关内容 | 1.深入学习希望后续可以制作出可免杀的木马 |      |
|                     |                                   |                                                    |                                          |      |
|                     |                                   |                                                    |                                          |      |
|                     |                                   |                                                    |                                          |      |
|                     |                                   |                                                    |                                          |      |
|                     |                                   |                                                    |                                          |      |

### ▶ 施皓文

| 时间                | 任务名称                                                                        | 本周进展                                | 下周计划            | 备注 |
| ------------------- | ------------------------------------------------------------------------------- | --------------------------------------- | ------------------- | ---- |
| **2025.2.28** | 安排badusb方案，能实现基础的usb脚本执行（后续可能会安排服务器的代理池相关内容） | 1. 靶场打靶<br />2. 搭建bad-usb所需环境 | 研究如何实现bad-usb |      |
|                     |                                                                                 |                                         |                     |      |
|                     |                                                                                 |                                         |                     |      |
|                     |                                                                                 |                                         |                     |      |
|                     |                                                                                 |                                         |                     |      |

## * [ ] 后续免杀结束之后，或者badusb方案有一定的成效之后，是否考虑安排他们关于终端主机的攻防测试（主要是主机安全方面的攻击，如木马、ddos等，不进行sql等服务器的攻击），以为了验证我们的信创系统是否有效。海博可以去找相关的木马库，作为网络攻防，特别是关于信创终端的攻击，需要有自己的常见的木马病毒库（最好有不同功能的木马，能做到基本免杀的，后续也方便对我们自己的信创终端进行对比测试）
