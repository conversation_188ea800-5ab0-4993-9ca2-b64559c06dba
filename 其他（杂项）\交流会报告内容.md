# 0. 自我介绍

大家好，我叫谢广东，非常荣幸能够以学长的身份参加此次交流会。今天，我想和大家分享一下我的职业经历和工作体验。

今年3月18日，我开始在西交网络空间安全研究院实习。刚入职时，我的主要任务是快速熟悉公司的产品，并参与安全芯片的IC功能验证工作。虽然我在本科和研究生阶段的学习主要集中在嵌入式应用开发和算法AI相关领域，但之前并没有接触过嵌入式底层的测试开发。因此，这对于我来说是一个全新的挑战。

为了尽快上手工作，我从两个方面入手：
1. **技术上的熟悉**：首先是在技术上熟悉IC验证流程。我主要的工作是查看案例中的波形图，确保功能实现与预期一致。在这一过程中，我花了大量时间研究寄存器地址偏移、位偏移，以及这些偏移所对应的具体功能。这让我深入理解了芯片的底层运行机制，也为后续的工作奠定了坚实的基础。
2. **市场调研**：我还进行了网络安全市场的调研工作。我通过对比一些行业头部厂商的技术和产品，更加深入地了解了我们研究院的产品定位和未来的发展方向。这不仅帮助我更好地理解了市场需求，也让我在技术之外，对行业有了更全面的视角。

经过几个月的实习和学习，我在6月17日正式签约，成为研究院的一员。正式入职后，我的工作内容也有所调整，开始负责网络安全中的渗透测试，并参与建立基于AI的网络安全防护平台。这项新的任务让我能够运用此前积累的技术经验，同时继续在网络安全领域深入探索。

# 1. 对研究院的初印象

## ① 对研究院的初印象
当我刚加入西交网络空间安全研究院时，最初的印象是，这里可能更侧重于科研方向。然而，随着我逐渐深入了解，发现研究院虽然具备强大的科研实力，但职能划分更趋向于企业化管理。这让我感受到这里不仅仅是一个学术研究的场所，同时也具备了高效的项目管理和市场导向的工作方式。值得一提的是，研究院的工作氛围非常友好和谐，虽然大家大多是技术人员，可能看起来有些内向，但实际上，团队合作非常紧密，而且研究院也会经常组织一些小型活动，比如下午茶、水果分享等，增进了同事之间的互动和交流。

## ② 对这份工作的期待
在求职初期，我了解到研究院的工作内容与硬件开发紧密相关，而我自身也具备嵌入式技术的背景，包括硬件、软件、控制、图像处理和数据分析等方面的经验，因此我觉得自己能够胜任这份工作。刚开始的面试并不顺利，主管提出了关于网络安全硬件技术的问题，而这正是我当时不太熟悉的领域。我当时很坦诚地表达了自己在这一块的不足，但也强调了我在嵌入式技术方面的特长，并提出了是否有其他相关岗位的可能性。幸运的是，几分钟后，主管给了我一个机会，安排了一个更符合我背景的嵌入式开发岗位。在此过程中，我对研究院的灵活性和对人才的尊重有了更深的理解，这也让我对这份工作充满期待。

（当然还有一个小插曲，就是投递简历，后来过了两天之后就收到我们技术主管的面试电话，主管上来就问的是我懂不懂网络安全方面的技术，比如硬件安全有哪几种方式，我毕竟没做过网络安全相关的硬件技术，我就直接说对于这方面不是特别懂，就实话实说了。我当时心想这下工作估计没了（但是我心想自己本科也参加了很多比赛，也拿了一些国奖和省奖，技术还有的，不能轻易放弃），就在技术主管准备挂电话的时候，我说还有没有其他嵌入式方面的岗位，主管说帮我问一问。接完电话后就挂了，我当时还心有余悸，好在没过几分钟，主管又打来了电话，说还有相关的岗位，然后就开始针对我的技术简历进行相关技术、比赛、项目的询问，主要问的都是嵌入式方面的，从整个项目到一些细节以及如何解决问题等等。最后主管说让我早点来实习磨合一下，我对这份工作也比较感兴趣，于是也在几天内就入职实习了。还未入职前对这份工作期待度还是很高的，毕竟涉及到自己未知的领域，或者说不熟悉的领域，但是我当时凭着自己的能力还是有信心能够胜任这份工作的，且心里淡淡的也有一种未知感。）

## ③ 工作实际情况
刚开始实习时，我确实花了不少时间来适应项目，特别是在IC验证中涉及的寄存器地址偏移查找和底层代码运行分析等方面。这些技术细节虽然看似复杂，但随着我的逐步掌握，我也渐渐适应了工作节奏。

## ④ 工作与学生时期的对比
相比于学生时期，工作中的挑战更具实际性，需要在有限的时间内高效地找到问题、分析问题并解决问题。而在研究生期间培养的思维方式和解决问题的能力，在实际工作中得到了很好的应用和扩展。实际上，不论是学生时代还是现在的工作，核心都是掌握一套属于自己的方法论——能够准确分析问题并制定切实可行的解决方案。在这个过程中，了解自己的技术优势，并灵活运用这些优势，是关键所在。

# 2. 新质生产力的核心

## 新质生产力：大模型时代——让我们每个人成为自己的PM

随着大模型时代的到来，计算机研究生在职业发展中不应仅仅局限于传统的编程和算法技能，而是需要培养多维度的能力，使自己具备更广泛的视野和更全面的技能组合，以适应快速变化的科技环境。

### ① 拓宽技能面
在大模型时代，成为一名优秀的产品经理（PM）不仅需要深入理解AI和机器学习的原理，这是驾驭大模型的基础。除此之外，数据分析能力也变得至关重要，因为现代产品管理越来越依赖数据驱动决策。这些能力使你能够深入洞察用户需求，并作出更加精准的判断。

与此同时，设计思维和用户体验研究同样不可忽视。这些技能能够帮助你更好地理解用户需求，进而设计出符合市场需求的产品。此外，商业分析和市场营销的知识也是你需要掌握的关键领域。通过跨领域学习，你将能够从多个角度分析问题，提出创新的解决方案。要记住，在这个快速变化的时代，终身学习的能力可能是最宝贵的资产，它使你始终保持在行业的前沿。

### ② 建立个人问题解决方法论
问题解决能力是产品经理的核心竞争力。在大模型时代，传统的"百度一下"固然重要，但我们现在有了更强大的工具。学会有效利用ChatGPT等AI助手，通过精心设计的prompt来梳理问题，不仅可以帮助你理清思路，还能够激发出新的创意。然而，关键在于培养批判性思维，不盲目依赖AI的输出，而是通过自己的判断和分析作出最终决定。

与此同时，建立个人的知识管理系统也至关重要。你可以使用Notion、Obsidian等工具，将碎片化的信息整合成结构化的知识体系。这不仅可以帮助你更高效地管理和运用知识，还能通过不断实践和反思，逐步形成一套属于自己的问题解决框架。这一过程就像培育一棵知识之树，需要持续的浇灌和修剪，才能让它枝繁叶茂，结出硕果。

### ③ 从项目管理到产品管理的进阶
从项目管理（Program Manager）到产品管理（Product Manager）的转变，本质上是视野和思维方式的升级。作为Program Manager，你可能更专注于项目的执行和交付，而成为Product Manager后，你需要培养战略性思维和商业敏感度。这意味着你需要开始关注市场趋势，理解用户痛点，并思考如何通过产品为用户创造价值。

为了实现这一转变，你可以尝试将日常生活中的观察转化为产品创意，培养敏锐的洞察力。参与开源项目或创建个人side project也是非常有效的途径，这些经历不仅能帮助你理解产品的全生命周期，还能提升你的实际操作能力。同时，学会与不同角色协作，平衡各方需求，确保项目顺利推进，也是成为优秀Product Manager的关键能力。

记住，成为一名优秀的Product Manager需要时间和经验的积累。保持好奇心和学习热情，勇于尝试和犯错，这将是一段充满挑战但极具价值的旅程。

通过以上三个方面的努力，你将逐步从一名计算机研究生成长为一位具有技术背景的优秀产品经理。这个过程不是一蹴而就的，而是需要持续学习和实践。在大模型时代，技术与产品的界限越来越模糊，这为具备跨界思维的人才提供了巨大机遇。保持开放的心态，拥抱变化，相信你一定能在这个充满可能性的时代找到属于自己的独特价值。

