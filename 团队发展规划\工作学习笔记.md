* [ ]

3.25-3.29
本周：1.学习了WDT、Timer、RTC等无协议的外设接口，掌握上述外设接口的时钟、频率以及相关寄存器的操作，并在例程中以模拟工作负载的方式，对上述接口外设进行测试及功能验证，如模拟时分秒时间输出、看门狗喂狗、定时器等功能
下周计划：接下来熟悉debug和模块 ，之后会根据布置完成一些关于外设等功能的实现

具体的调试信息（比如变量、断点、数据观察点、寄存器、内存、反汇编、调用栈等），还可以展示数据采样信息、指令跟踪信息、甚至是实时功耗信息等，可以帮助我们分析代码的覆盖率和执行效率，帮助我们定位软件执行bug 与瓶颈。

---

本周：了解掌握DMA、QSPI这类有协议的外设使用方法的例程，对QSPI的直接访问、间接访问两种工作模式进行模拟数据传输并进行功能验证，在对QSPI功能验证的过程中，在主函数进行cqspi_write的flash从0x00写入与读取时，发现与bootloader的几种启动方式相冲突，导致程序无法进入cpu，且cpu挂掉
下周：具体分析bootloader的几种启动方式与Flash之间的关系，以及出现上述问题的具体原因

---

本周：搭建并熟悉linux仿真环境，了解并掌握linux、verdi的一些基本操作，以简单的gpio case为例进行verdi信号学习
下周：根据具体的一些实际案例case，并结合手册与mux_pin进行更深入的学习

---

具体的svn地址如下：
svn://************/Project

代码svn地址如下：
svn://*************/project1/RulaiSA2A/

D:\svn\RulaiSA2A\rulaisa2a\verify\bootloader_sample\system\chip_env\testcases\bootloader\ts_bootloader_ap\tmp\image\cc_cases\src_top\GPIO

分析case.c中的主函数与其verlog语法之间的关系，

---

本周：掌握简单的gpio高低电平操作，利用verdi的paddr以及pwdata，并结合代码和寄存器手册的地址偏移与位位置，了解代码运行及验证的整体逻辑，现在正在进行gpio_intr中的8种中断方式的case以及verdi验证
下周：掌握gpio_intr的流程方法以及分析验证sec_gpio与gpio之间的区别

---

本周：
1.掌握了解gpio及sec_gpio的gpio_intr中的8种中断方式的case以及verdi验证，特别是pclk与gpio_clk中断的区别以及跳变沿和高低电平触发中断的区别；
2.帮助验证enter_loadmode、enter_io_mode6、7、8四种启动模式，用afx及代码配合signals进行关键寄存器写入值的查找，从而验证功能是否对应实现。
下周：
1.继续深入了解关键的case，如case_uart.c 和 case_i2c.c，新增背景调研国内外的安全平板或者安全PC，即（比如国外的安全平板在安全方面是怎么去保护个人数据，数据传输如何保证安全的），我们这边在做产品定义的时候，可以参考和完善我们自己的产品。

技巧心得：
每次svn update都要进行删除system，然后./set_env，如果涉及到反汇编的需要axf结合/trans/temp/xieyh下面的signals观察是否正确。

未来学习规划：

从您已经对GPIO等简单中断机制有了一定了解的基础上，我们可以逐步深入到更复杂的IC验证领域。您上传的文件列表中包含了多个测试用例（case）的代码文件，这些文件都是用于验证“赛安2号”芯片不同模块的功能和性能。根据由易到难的原则，您可以考虑按以下顺序逐步深入学习和验证：

1. 从基础的通信和控制接口开始

- **case_uart.c** 和 **case_i2c.c**: 了解基础的串行通信接口，UART (通用异步接收发送器) 和 I2C（两线串行通信协议）是两种基本的通信协议，涉及数据传输的基础知识。
- **case_ssi.c** 和 **case_spi.c**: 进一步理解同步串行接口（SSI）和串行外设接口（SPI），这些通常用于连接高速设备如闪存。

2. 理解数据存储和访问

- **case_sdmmc.c**: SDMMC用于SD卡访问，涉及数据存储和文件系统，这是一个比较实际且常见的验证点。
- **case_flash.c** 和 **case_qspi.c**: Flash存储和QSPI（快速串行外设接口），涉及非易失性存储技术，更深入地理解硬件与存储介质的交互。

3. 深入电源管理和性能控制

- **case_pwm.c** 和 **case_poweroff.c**: 了解脉冲宽度调制（PWM）控制和电源关闭处理，这涉及到电源管理和能效控制，对系统优化尤为重要。
- **case_pwr.c** 和 **case_suspend.c**: 这些测试用例涉及更复杂的电源管理策略和休眠模式，是理解系统如何处理低功耗状态的关键。

4. 高级功能和系统级集成

- **case_dma.c** 和 **case_dmag.c**: 直接内存访问（DMA）相关的验证非常关键，它涉及到高效数据处理和系统性能的重要部分。
- **case_tzc.c**: 信任执行环境的配置和验证，这是高安全要求系统的重要部分，理解和验证安全区域的配置和管理至关重要。

5. 特殊功能和复杂模块

- **case_timer.c** 和 **case_wdt.c** (看门狗定时器): 这些是系统的基础时间和监控机制，对于保证系统可靠性非常重要。
- **case_nfc.c** 和 **case_pcm.c**: 这些涉及特定的应用场景，如近场通信（NFC）和脉冲编码调制（PCM），适用于需要特定通信协议和音频数据处理的应用。

从基础的串行通信学起，逐步过渡到更复杂的数据管理和电源控制，再到系统级的集成和安全验证，将帮助您全面深入地理解和掌握IC验证的各个方面。每一步的深入都构建在之前的知识上，确保您能够有效地积累经验并应对更复杂的验证挑战。

---

本周：1.针对赛安芯片的学习，学习了case_i2c读写验证，了解整体的运行逻辑及方法。
2.针对国内外的安全PC以及国内的网络安全调研，已完成了基本的框架，即从市场背景、案例、未来发展方向与技术趋势、技术分析与策略定制四个方面进行强有力的论述。
下周：继续学习赛安芯片的case；具体完善调研报告

---

本周：结合中国网络安全产业分析报告以及办公设备安全规范标准进行系统性的论文和逻辑思路的整理，以总体市场分析为引入，在从我们的产品和需求进行更深入和细致的论述（主要涉及办公安全方面）
本周：
1.根据调研基本框架，对第一章节“市场背景”的内容及逻辑进行完善，并增加图表分析，对小节细分进行更加细致话的论述
2.学习赛安芯片的uart case，并进行仿真验证
下周：完善剩余的调研内容，并继续学习赛安case

分析数据安全的几种方法，进行更深入的分析

本周：
1.针对网络安全的调研报告，重新对第二章和第三章的结构进行梳理，并在第二章中增加了部分网络安全企业的竞争产品案例分析
2.SA2A后仿真，目前还在跑
下周：
1.继续对调研报告进行内容及结构上的完善
2.继续对后仿真case进行验证

6.17-6.21
本周：
1.针对后仿，开始第二轮重新跑case进行验证
2.关于调研内容，在第二章中增加了并论述了市场需求分析（主要涉及到终端安全、数据安全）、以及对分析了17家网络安全公司的主流产品，梳理了部分公司可借鉴行业解决方案。
下周：
1.持续深入研究网络安全公司的产品及方案 ，力求全面了解行业现状，为产品开发提供有力支持。
2.继续对后仿case跟进

---

6.24-6.28

本周
1.针对调研内容，对第二章的结构进行优化（条理论述更清晰），并在第三章“前瞻技术研究与应用策略”对主流技术进行详细论述（包括概述、实施步骤、应用案例、挑战与局限），已完成网络安全调研第一版（word形式，包含基本框架和内容）
2.进行bootloader的case后仿工作，跟进中
下周：
1.根据赛安产品手册的具体要求，继续往物理硬件方向进行更深入的研究，（同时大厂的软件方向的调研也要跟进）争取更多的产品创新点，提高市场竞争力
2.继续跟进后仿case

---

7.1-7.5

1.针对渗透攻防，最好在麒麟系统上先实现（第一步，可以在windows上先实现简单的木马病毒或者其他攻击方式等）；并收集病毒库、漏洞库进行整理，用ai进行相关训练；src漏洞分析（只要找到漏洞就可以进行攻击）

本周：
1.利用Python学习基本的渗透测试方法，了解Python渗透的基本流程，目前使用nmap先对局域网内主机进行渗透测试
2.继续跟进后仿bootloader case
下周：
1.学习kail及其他主流渗透工具，如metasploit，对比分析这些工具之间的优缺点差异
2.继续跟进bootloader case
3.查找相关病毒库以及漏洞库用于辅助ai训练

7.15-7.19

idea：后期可以用Python做一个正常的网络数据灰度图收集软件，然后打包安装到每个人的电脑中，这样就能收集正常的数据类型；另外对于非正常类型的数据通过渗透测试进行采集；后期可以制作两个攻防演练的模型，自动化采集数据。

本周：
1.开始进行ss后仿工作
2.收集了相关病毒库、漏洞库数据（来自美国NVD）
3.与陈博开会讨论了关于ai部署在赛安2号的若干建议，目前确定基本方向是采用yolo（或者其他轻量化模型）对不同的网络特征进行训练分类（如数据包长度、协议类型、源端口、目的端口、TCP标志位、负载数据长度、源IP/目标IP的4个字节），从而拦截非法访问等操作。（目前利用Python收集自己主机的正常网络数据进行特征分析）

下周：
1.对基于ai的网络防护方案进行验证，依次从数据采集、特征处理、模型选择、模型训练等几个方面进行验证。
2.继续跟进后仿ss的case
3.进行局域网渗透测试

---

7.22-7.26

本周：
1，进行ss后防工作。
2，针对网络特征分析，以进行的正常网络的16组特征验证（采集5000组左右数据进行初步分析），考虑到后期的模型部署，并再次基础上优化网络特征提取速度（优化用户体验），并制定了基于网络流量特征的AI防护基本流程方案。
3.进行渗透测试kali的初步学习及kali搭建。
下周：
1.继续跟进后仿case
2.进行相关渗透测试，采集部分非法数据/攻击数据与正常数据进行特征对比分析，从而进一步确定这些特征的可行性

---

7.29-8.2

本周：
1.进行ss后防工作，已完成ss的6个后仿
2.优化特征提取方案：进行更细粒度的归类（全面的特征提取：包括包级、流量、会话、窗口、统计、上下文、应用层、加密、网络结构和异常检测特征），并将特征按照计算成本进行排序，优化整体方案策略
3.优化特征提取代码：增加异步处理、多进程、线程池、数据结构优化、增量计算、特征分层（不同特征计算频率不同）、错误处理及日志、命令行参数解析、实时性能优化，目前在对这些代码系统及功能进行验证
下周：
1.继续关注后仿case
2.验证新的特征提取功能，以及在此基础上进行后续的正常流量/非正常流量收集分析

---

8.5-8.9
备注：做工作汇报的前一两周需要进行每周，甚至细化到两三天内的任务细化，可以用gpt等进行总结，这样就能记录工作内容
后期优化：了解时序数据库相关知识（后期可以采用时序数据库influxDB进行高效的存储、追溯、分析），进行代码的部分优化（gpt对比可得出）
根据上述的特征冗余对比后，新增以下
（网络流量统计特征、协议异常检测特征、连接行为特征、数据包内容特征、时间序列特征、系统日志特征、网络拓扑特征、用户行为特征）

本周：
1.进行ss后防工作，已完成ss的8个后仿
2.进行了网络流量特征分析：
-使用相关性矩阵热力图和VIF分析识别了高度相关和存在多重共线性的特征，制定了特征选择策略
3.优化特征提取方案：
-添加了新的特征如compression_ratio、has_hex_encoded_content、has_base64_content等；
-优化了HTTP特征提取，增加了is_http、has_user_agent、has_cookie等特征，提高了对潜在恶意流量的识别能力；
-新增了PacketContentAnalyzer类，增强了对数据包内容的分析能力。

下周：
1.继续跟进case，并开始ff后仿真
2.继续对网络特征进行多个维度的特征分析
3.根据特征分析结果，进行闭环反馈优化，致力于提供可靠的网络特征

---

8.12-8.16
本周：
1.对特征进行第二次验证包括特征分布分析、特征相关性分析、PCA特征分析，且在此基础上修复数据缺失与异常、数据类型不一致、PCA重复特征或冗余、实现动态精度控制，此外对网络流量“地理位置”信息数据库进行更新，目前正在优化
2.进行后仿真ss及ff
3.梁总的北邮交流会，包括会前的相关问题的准备，心得以及工作历程、学习方法论等。
下周：
1.继续跟进case
2.针对网络特征进行随机森林或SHAP值等更多的方法来优化特征

---

8.19-8.23

本周
1.进行bootloader的后仿真ss及tt
2.进行了网络特征的冗余分析，主要有基本特征分析、PCA、VIF、随机森林、SHAP值等方法进行去冗余
3.进行其他基于python高级渗透的学习，如通过网络拓扑结构，分析获取目标主机ip
下周
1.继续进行后仿case跟进
2.将特征采集部署到麒麟系统中，进行一些攻击手段，采集一些非法数据，为后续的模型训练打基础
3.研究非物理侵入的方法，进行拓扑式网络渗透测试

临近局域网的嗅探侦查
<分析: 在不知道目标网络具体信息的情况下，我们需要一个更加动态和自适应的网络发现过程。这个过程应该能够从已知网络开始，逐步探索和扩展到相邻网络。>
<技术: 我们需要结合被动和主动扫描技术，使用更智能的网络发现算法。同时，我们还需要考虑如何在不触发安全警报的情况下进行探测。>
<创新: 考虑使用机器学习算法来分析网络流量模式，推断网络结构和可能的网关位置。>
基于这些考虑，我提出以下改进方案：

1.网络发现流程改进：
a. 从本地网络开始扫描
b. 识别可能的网关和路由器
c. 逐步扩展到相邻网络
d. 使用启发式算法推断网络边界
2.被动网络嗅探：
a. 捕获和分析网络流量
b. 识别常见协议（如ARP, DHCP, DNS）中的网络信息
c. 推断可能存在的其他网段
3.主动但谨慎的探测：
a. 使用低带宽、低频率的探测技术
b. 实现自适应扫描速率，避免触发IDS/IPS
c. 使用伪装技术，如模仿正常网络流量
4.网关和路由器识别：
a. 分析网络流量中的TTL值
b. 使用traceroute技术识别网络路径
c. 探测常见的路由器管理接口
5.网络结构推断：
a. 使用图论算法构建初步网络拓扑
b. 应用启发式规则推断可能的网络分段
c. 使用机器学习模型预测未知设备的类型和功能

---

本周
1.进行bootloader的后仿真ss（nand aes及sm2 crc运行在60ms左右）及tt （17ms左右）
2.在麒麟系统下安装python环境
3.最后对网络特征进行了一次验证，包括shap值、随机森林重要性、互信息分析、PCA分析、热力图分析、Lasso、ElasticNet，从而确保特征的有效性，并在此基础上优化特征计算效率
下周
1.继续跟进后仿case
2.在麒麟系统上部署特征提取代码，并采集非法攻击下的网络特征
3.对非法网络以及正常网络下的特征进行对比分析，如果可行再进行模型训练

---

本周
1.进行bootloader的后仿真ss及tt，目前都只剩下aes及sm2算法仿真；
2.对特征提取代码制定了gui界面，并将整体打包为exe，方便他人执行和操作，并在此基础上进行代码结构和逻辑优化，如数据保存、print进度条提示、模式选择等细节；
3.目前正在分析如何对对数据采集进行细节把控，如如何制定一个非正常数据的采集标准
4.陪同梁总一起去拜访露笑科技有限公司；
下周
1.继续跟进后仿case
2.制定详细且完整的非正常数据采集标准，以及一些细节，如采集冷却期、精确的时间点标记等，并将这些细节优化到采集系统中
3.尽可能多的实行攻击手段，如基础扫描和探测、拒绝服务攻击、暴力破解、漏洞利用等

---

本周
1.进行bootloader的后仿真ss及tt，目前剩下aes及sm2算法仿真；
2.由于网络流量的波动性，采用高频采样策略结合滑动窗口机制：实现可变大小的滑动窗口（1分钟、5分钟），以捕捉不同时间尺度的网络行为，并在这个过程中解决了一些升级bug：dst_port特征为空白行、为0值问题、最小特征单位标志位隔断等，并在此基础上对时间戳信息进行优化，优化为相对时间信息
3.目前正在解决相同时间内采集数据不一致问题（数据尺度归一化问题）
下周
1.继续跟进后仿case
2.解决特征时间尺度归一化问题

1.针对非正常数据采集的尺度标准化问题进行分析（数据送入模型需要归一化），有以下几种方案对比：多尺度模型进行训练、滑动窗口归一化处理、非监督学习（不对攻击数据进行训练，只训练正常数据）、

3.由于网络流量的波动性，采用高频采样策略结合滑动窗口机制：实现可变大小的滑动窗口（1分钟、5分钟），以捕捉不同时间尺度的网络行为，并在这个过程中解决了一些升级bug：dst_port特征为空白行、为0值问题、最小特征单位标志位隔断等，并在此基础上对时间戳信息进行优化，优化为相对时间信息

4. 
5. 时间戳（timestamp列）间隔不规律
6. 每个时间单位（由"---"分隔）包含的行数不一致
7. 某些特征（如dst_port）在单个时间单位内可能有多个不同的值
8. 部分数值可能异常（如负值的flow_duration）

目标：
优化特征提取过程，生成时间一致、特征统一的数据集，适合机器学习模型训练。

2.加入对连续特征的MinMaxScaler标准化

---

下周任务：优化特征、归一化问题

本周
1.添加了一些新的特征（包括数据包大小、时间特征、内容特征和行为特征），并修复流量接口自动选择问题、优化dst_port及protocol特征为独热编码（由于大多数特征都是时序特征，独热编码能消除了类别之间的隐含顺序关系）、优化max_packet_size 和 min_packet_size 特征归一化后分布问题（分布不均匀、标准差过小，利用精度保存进行优化），并实现必要特征归一化处理
2.添加新的应用层 DPI 特征，如 HTTP 方法计数、URL 长度、DNS 查询计数和 SSL/TLS 版本计数。
3.在数据集中添加了背景流量，模拟真实网络环境，使模型能够学习正常与异常流量之间的差异。

下周
1.对gui界面进行细节优化，开始对进行正常/非正常的数据采集

---

本周：
1.为了得到模型的训练效果，优化代码为“监督学习/非监督学习”数据同时采集（统一采用归一化后的数据进行采集）。
2.优化gui界面，将“数据开始采集”细分为“正常数据采集”“攻击数据采集”，并在后端对攻击数据的记录进行逻辑优化，即攻击数据采集完毕填写攻击方的开始/结束时间戳，这样能获取更为精准的攻击数据。
3.在麒麟系统中搭建环境并运行了特征采集系统，并解决了一些运行权限问题（目前还有个小bug，在麒麟系统中无法退出界面，正在解决）。
下周：
1.解决麒麟系统下软件退出的小问题，以及正式采集攻击/正常数据
2.并在此基础上做特征对比分析，形成反馈优化

---

本周：
1.特征采集系统在麒麟系统下的适配问题修复，如sniff()函数在不同操作系统下的阻塞现象、os.kill(os.getpid(), signal.SIGINT)中断信号问题，目前正在采集正常数据。
2.帮助海博完成PPT模板以及文案内容逻辑思路上的优化等。
3.测试CVE-2022-0847 漏洞、查找相关的木马病毒方案。
下周：
1.进行正常网络数据的分析，并构建训练方案
2.建立基础的非监督训练模型，先对正常数据进行训练

---

本周
1.再进行ddos数据采集的时候，发现我们的系统在处理这种高并发的数据时无法对全部的数据进行数据特征的处理，采用基于流量大小以及数据队列的动态采样率方法进行数据采集。
2.对西安交大的陶敬和孙钦东老师的论文进行相关工作的借鉴分析。
3.目前正在对ddos高并发的流量数据进行采集时，发现性能瓶颈，从而导致部分特征为0，并从CSV写入机制、减少锁竞争、采样率调整策略等方面进行优化。
下周
1.解决ddos等高并发流量的数据采集问题
2.采用非监督学习方法构建模型，采集并对正常数据进行训练

---

10.28-11.1
本周
1.利用基于流量和队列的方法完成动态数据采样率，解决了高并发情况下的数据采集情况。
2.采集了简单的ddos数据，并进行了合理性分析
3.采用非监督学习的方法，进行了简单的IsolationForest数据训练。
下周
1.采集更多的正常网络流量数据以及ddos攻击数据用于训练
2.进行更多模型的测试，如多种非监督混合模型、时序模型等，并测试效果

---

11.4-11.8
本周
1.采用iforest、lof、ocsvm混合非监督模型，训练了(1320546, 20)正常网络流量数据。
2.基于上述训练的模型，构建实时入侵检测系统（RealtimeIDS），并有多重阈值检测报警输出，能进行非正常流量的异常流量检测，如ddos攻击。
3.目前正在采用自编码器模型进行训练，但是由于特征数据的不一致导致loss过大模型无法收敛，正在对数据进行分析。
下周
1.寻找特征部分不一致导致的模型训练不收敛问题
2..在自编码器模型的基础上进行验证，并对比混合非监督模型的效果

---

本周
1.根据上周自编码器的训练结果，由于部分特征归一化后的数值较大，如bytes_per_second、packet_frequency，根据模型结果对特征进行反馈优化。
2.优化了端口、protocol数据，取消了独热编码，改为计数特征统计、对数变换，再进行EWMA归一化，兼顾数据变化和模型收敛能力。
3.去掉部分对模型贡献不大且数据变化趋势不明显的特征，如dns、ftp、http、ICMP等相关特征，添加加密解析特征SNI、目标端口熵和源 IP 地址熵相关特征。
下周
1.根据上述确定的特征重新进行正常数据的采集
2.根据采集到的数据，训练模型，验证效果

---

本周
1.基于上周特征选择后的优化数据，进行基于1dcnn+gru的自编码器模型训练（异常检测，还不能进行具体的哪种类型的攻击），并对模型进行验证（正常数据/非正常数据），目前由于数据量、模型架构问题，准确率还在优化。
2.为实习生制定相关渗透入门基础知识、网络协议、web安全、内网渗透等学习任务，并由此拟制定网络安全攻防小组的未来发展方向。
3.基于上周优化后的特征，进行iforest、lof、ocsvm混合非监督模型的训练，利用该模型，训练的实时ids效果优于自编码器模型。

下周：
1.对自编码模型进行优化，提高其准确性
2.与混合非监督模型进行实时ids的效果对比分析

---

本周
1.处理了实时ids中的特征提取的数据流顺序一致性问题，即数据流的输入要和模型训练时的输入保持一致性；解决了实时特征提取中的回调函数进入实时ids时的不一致问题，以及优化实时ids数据批处理问题。
2.在进行1dcnn+gru的模型训练时，对数据采用数据增强，增强模型表现；并优化了实时ids中的模型与实时特征数据流匹配的问题。
3.对网络安全行业及部分企业的调研

下周
1.继续对上述的两种优化后的模型进行对比验证
2.尝试在更多的攻击环境下进行模型能力验证

---

本周：
0.参考部分网安企业的调研报告，对网络攻防小组的未来规划进行思考优化，确保其可实践性，以及小组部分业务、技术目标设定。
1.查找关于非监督学习在入侵检测系统中的应用和挑战相关资料，对入侵检测未来方向进行优化，如增加元学习、小样本学习；以及在不同场景下正常数据的采集问题；模型在不同网络环境下的泛化问题。
2.优化特征提取gui界面，增加“启动背景流量”按钮以及后端交互逻辑，并采集了下载、上传、远程控制，等不同场景的正常流量。
3.学习前后端web安全的交互逻辑，了解常见主流前后端以及数据库框架，并以vue3+flask+mysql为学习对象，进行web安全方面的研究学习，从而更好地对web安全的研究进行规划。
下周：
1.将采集的多种场景下的数据继续进行监督学习与非监督学习的训练。
2.学习web安全方面内容，从简单案例中学习常见web逻辑漏洞。

- [ ] ==元学习和小样本学习（少量恶意样本大量正常数据）==：

- **ProtoNet（原型网络）**:
  - 通过学习类别中心向量处理分类问题，适合小样本和动态更新场景。
- **MAML（Model-Agnostic Meta-Learning）**:
  - 允许快速适应新任务的模型优化技术，特别适合处理网络入侵数据中的多样性问题。

- [ ] ==3.构建框架或者脑图对数据特征提取以及归一化处理、多重检测模型训练、1dcnn+gru自编码器模型进行框架梳理==
- [ ] ==deep svdd在特征空间中学习正常数据周围的超球体==
- [ ] ==模型在不同网络环境下的泛化能力==

---

1.查找信创产品的在攻防方面的痛点，根据痛点进行相关攻防方面的细致化研究，如何在入侵检测的基础上增加蜜罐等其他防御措施。

本周：
1.调研并对比基于操作系统API恶意软件识别的方法的主机防御方法，通过分析文件操作、注册表、网络、进程/线程、内存操作API的调用序列、调用频率、参数特征、调用上下文等对恶意软件和正常软件的api信息进行训练。
2.结合自身项目对比研究主流的入侵检测数据集，如NSL-KDD和CICIDS2017，主要对比了数据集的特征以及攻击类型，从而为模型验证做铺垫。
3.对神经网络自编码器模型的重构误差代码进行优化，如采样率调整等，目前根据结果一步一步反馈优化再训练，并对多重模型检测进行验证。
4.优化组内任务规划，对于攻击方面，从两个方面进行研究，即从工具使用和web安全实操两个方面研究。
下周：
1.继续训练模型，并进行一定程度的验证
2.继续调研并学习网络攻击、网络防御方面的知识，指导小组方向

---

本周：
1.优化了传统的无监督学习的多重混合模型，分别从基于随机树隔离、局部密度比较、多高斯分布、密度空间聚类五个子模型进行混合学习验证，并增加了超参数调优，由于超参数导致训练时间较长（目前在想办法优化）。
2.整理归纳了基于常见的红蓝攻防对抗所用技术以及框架方案，且对攻防小组方向进行细节优化。
3.调研网络攻防过程中的一些法律问题，如合规情况下的渗透（测试）平台选择、渗透对象选择、渗透程度等。
下周
1.继续学习网络攻防知识
2.训练优化后的入侵检测模型

---

本周：
1.学习相关的yakit靶场漏洞及sql基本语法，如常见sql漏洞，目前这个靶场的sql漏洞存在一些问题，正在分析。
2.调研了开源HIDS（主机入侵检测系统）的工作原理，如OSSEC和WAZUH，了解其通过监控系统活动，然后对比预定义的规则集进行入侵检测。
3.优化了多重模型检测，由于超参数的LocalOutierFactor的空间计算复杂度为O（n^2），因此对训练参数、样本数进行优化，并整理当前入侵检测的若干问题及分析。
下周
1.在麒麟系统部署靶场，学习web常见攻击方法
2.继续对入侵检测系统进行规划

---

本周
1.研究并对yakit靶场进行测试，对其中的数字型sql注入例程进行测试分析，研究靶场源代码，并通过数字遍历标记法获取sql数据库数据，采用UNION SELECT等绕过技巧，并整理相关笔记及过程记录。
2.研究信创主机安全的防御系统，如HIDS，并与目前的基于ai的网络入侵作对比分析。
下周
1.继续学习yakit靶场的漏洞实践
2.继续研究学习网络攻防相关知识，并对小组任务进行合理安排规划

---

本周
1.在yakit靶场，主要学习了相关的字符串sql注入方法，主要以limit、order等案例，进行模糊查询的相关绕过技巧学习，以及其他字符串的边界、括号、注释等绕过技巧。
2.学习xss注入方法，并构建相关注入prompt进行帮助学习，主要学习了拼接字符串注入，以及以及结合不同编码格式结合的事件注入，如鼠标、点击、交互事件等。
下周
1.学习SSRF 参数多种情况的测试注入
2.学习高级前端加解密与验签实战

---

本周
1.学习了高级前端加解密与验签，掌握CyberChef的基本使用方法，以及key+哈希算法（RSA、AES）的基本加密流程，以及基本的yaklang脚本
2.对fastjson相关案例进行实践，具体为分析fastjson解析恶意构造的JSON数据时，进行注入，遇到dns域名问题，导致效果不明显。
3.制定信创笔记本攻防演示方案

下周
1.信创笔记本攻防演示
2.继续学习web攻防相关知识

---

本周
1.优化了整体的信创笔记本演示方案，包括流畅度、安全方面的对比，以及其中的一些细节。
2.制做基于Windows的木马（如弹窗、伪装pdf、鼠标不受控等），并基于此对木马脚本进行优化重构，使其适配于信创系统的表现。

年后
1.继续负责网络攻防相关研究和实战
2.继续优化信创笔记本演示项目

---

# 2025/2/8

本周
1.研究HIDS配合AI防御的整体方针，对比了多种hids搭配选择，选择先Wazuh进行二次开发，并进行了初步的相关的部署工作，如wazuh-user、wazuh agent.
2.查阅Wazuh相关论文，分析在此基础上如何添加相关AI需求，如规则匹配与LLM结合的优化、AI增强的日志分析场景等。
下周
1.进行相关的wazuh测试
2.研究如何进行相关AI需求的部署

> [!Tips] 实习生方向
> 继续研究学习web漏洞相关，并定期进行相关演示，未来可以进行相关漏洞证书的挖洞，从而为研究院提高影响力，也为未来进行相关的等保测评、护网行动服务。

 **1 主流 HIDS 对比**

| **维度**           | **Wazuh**             | **OSSEC**           | **Elastic Endpoint Security** | **Tripwire**          |
| ------------------------ | --------------------------- | ------------------------- | ----------------------------------- | --------------------------- |
| **核心功能**       | 日志分析/FIM/合规检查       | 日志分析/FIM/rootkit 检测 | EDR/行为监控/内存防护               | 文件完整性监控              |
| **Web 攻击防护**   | 中（依赖日志规则）          | 低（需自定义规则）        | 高（行为模型+威胁情报）             | 无                          |
| **资源占用**       | 低（Agent <50MB 内存）      | 低（类似 Wazuh）          | 高（Agent >500MB 内存）             | 中（定期扫描时 CPU 占用高） |
| **云 API 支持**    | 完善（RESTful API/Webhook） | 有限（仅 CLI 控制）       | 完善（Kibana API）                  | 无                          |
| **二次开发灵活性** | 高（开源/模块化架构）       | 中（代码老旧）            | 低（部分闭源）                      | 低（商业版闭源）            |
| **信创适配成本**   | 低（已支持 ARM/国产 OS）    | 中（需移植旧版本）        | 高（依赖商业支持）                  | 高（需定制代理）            |

---

本周：
1.优化关于信创笔记本的演示方案，涉及到文案（去口语化等）、素材等。
2.调研近源攻击的几种方法，如BadUSB、USBAirborne、USBkiller、GrabAccess等，以及如何配合Cobalt Strike实现攻击流程。
下周：
1.继续完成wazuh的相关部署工作
2.研究badusb的近源攻击方法

---

本周：

1.组内任务分配，针对围绕信创产品的“攻击”业务进行任务优化调整，主要调整为将“攻击”研究围绕“终端安全”“企业内网安全”展开；

2.研究免杀相关内容，及免杀任务分配；部署wazuh遇到证书相关问题，正在解决

下周：

1.部署wazuh

2.跟进badusb项目

> 主要安排如下：海博研究内网渗透相关的知识，如横向移动，“具体如一台局域网内的设备，处于开机状态，也没有密码设置，你知道对方IP地址，然后你要扫描出对方I设备信息，之后看如何入侵？那么你该如何在无接触的状态下对目标机器进行cs远程控制呢（当然也会涉及到免杀）”，所以我们现在攻击能力不足，也就是后渗透能力。

> 两位实习生：皓文有一些硬件基础，那么先负责badusb方案，资料我已经找好了。海波研究免杀，怎么样制作木马通过安全软件的检测，并能够实现cs等入侵，当然用cs制作的木马是最基础的教程，自定义程度最高的就是用Python等脚本语言制作最好；

---

本周：
1.解决wazuh部署过程中的证书问题，以及wazuh index的索引模板问题。
2.解决wazuh的agent在与manager主机连接过程中的代理名冲突问题。
下周：
1.测试wazuh的agent与服务器通信情况
2.继续带领小组研究badusb方案

---

本周
1.研究wazuh结合ai相关的案例，主要包括构建哪些功能，如“Ai驱动的用户管理界面”，即在 Wazuh 控制面板中引入了一个类似聊天的界面，用户可以输入安全相关的问题并查询模型。
2.配置wazuh agent添加相关的网络检测组件，如suricata，并对比了与其他NIDS（Snort）之间的区别，Snort其更符合信创终端的需求，如规则配置、轻量化。
下周
1.继续测试wazuh agent与服务器的功能，如网络层攻击
2.继续进行badusb方案的任务分配与跟进

---

本周
1.测试了agent与wazuh服务器之间的常见主机安全，主要包括创建关键文件修改(FIM测试)、生成失败登录(安全事件测试)、创建特权测试文件、服务器均能匹配到相关操作的日志情况；
2.正在测试wazuh相关高危安全事件、如反向shell、敏感文件访问、提权操作、网络层攻击等
下周
1.继续对wazuh相关主机安全内容进行测试；
2.继续跟进badusb相关进展；

---

本周
1.对比三种wazuh规则集，如SOCFortress、其次官网规则、最后进行sigma，并分析了哪种更符合终端安全的需求；
2.在wazuh规则的测试中，发现大多数警告只发生在5-7级，没有触发7级以上更高的风险，正在分析问题所在。
下周
1.继续对wazuh已知问题进行分析，并进行深入的终端安全测试；
2.跟进badusb相关进展；

snort安装，并进行连通性测试；进行最新规则的测试

测试对比三种规则集，优先SOCFortress、其次官网规则、最后进行sigma

---

本周
1.分析了wazuh服务器端与客户端agent的日志一致性与整体工作交互逻辑，并进一步分析了wazuh服务器端的rootcheck 510规则，以及agent端的rootkit_files、rootkit_trojans文件，针对规则进行相关的超过7级的rootcheck测试（去网上找找相关的poc 木马进行测试）
2.针对badusb项目，进行组内的讨论交流
下周
1.在agent端安装ClamAV组件进行木马病毒相关测试
2.继续跟进badusb项目

2.分析了wazuh服务器端的rootcheck 510规则，以及agent端的rootkit_files、rootkit_trojans文件，针对规则进行相关的超过7级的rootcheck测试（去网上找找相关的poc 木马进行测试）

（**如果你追求最真实的 Rule 521 模拟，并且愿意承担高风险和学习成本：****LKM****是最直接的方式。务必寻找**极简的、仅用于测试隐藏功能的 POC，并在一次性虚拟机中操作。）

使用 EICAR 文件进行病毒测试，EICAR (European Institute for Computer Antivirus Research) 标准反病毒测试文件是行业标准，专门用于测试反病毒软件的响应， **它本身完全无害** 。它是一个简单的文本文件，其包含的特定字符串会被大多数反病毒引擎（包括 Wazuh 可能集成的 ClamAV 或其自身规则）识别为“病毒”。

**ClamAV (或其他本地 AV):**

* **ClamAV 服务是否在 Agent 上运行？ (**systemctl status clamav-daemon **或类似命令)，并对比日志上报+yara实现恶意软件识别的实现方法**

sca的作用，如何找到大于7级的规则，根据规则制定相关的测试

以及如何通过wazuh进行安全评估

---

本周
1.对比分析了多种免杀实现，如通过视觉脚本退出免杀软件（适配性、稳定性不高）、通过脚本进行AV软件的进程清理、白加黑dll劫持，并通过免杀工具实现了cs beacon免杀。
2.借鉴免杀工具的思路，参考免杀工具的功能，用go语言实现自定义加密链、多种 Shellcode 注入技术，从而实现自己的免杀，目前还在调试。
下周
1.用go语言实现基本的免杀
2.wazuh测试相关内容

画出整体的wazuh交互流程图；

对比商业软件，宁盾、天擎；

测试agent木马；

---

本周
1.已实现基本的免杀（目前在解决火绒检测，其他主流检测能通过），对算法等进行优化，并尝试了更多的组合进行免杀
2.用rust从静态加载、动态加载方面解决了木马被火绒检测的问题，如内存操作隐蔽、动态密钥与IV、反沙箱检测等
下周
1.跟进badusb项目
2.跟进wazuh项目

tips.：用rust从静态加载、动态加载方面解决火绒检测的问题，如API Hooking规避、内存扫描规避、加密分割混淆、添加随机噪声等

---

本周
1.尝试一些免杀工具以及代码项目，目前能绕过360、火绒的静态检测，以及火绒的动态检测，正在尝试突破360的qvm动态云查杀检测。
2.研究数字签名相关内容，对木马文件进行签名能绕过检测；研究beacon的Malleable C2配置。
下周
1.逐步分析360动态杀毒特征，如cs通信、执行流程、合法行为等多个角度
2.用wireshark专业流量分析软件对木马通信进行进一步分析

---

本周
1.对木马进行优化，减少或变形可疑的运行时行为和内存特征，具体为内存分配策略优化、Shellcode执行策略优化、Shellcode解密时机与方式
2.研究对木马exe的随机生成图标功能，从而提高木马可信度
下周
1.继续研究针对360的木马免杀，重点为动态检测

tips：致盲可以过360晶核

---

解决目前遇到的exe退出问题（带有调试信息的.\build.bat --debug）、测试新的beacon.bin、按照之前的技术文档一，继续对exe进行免杀

本周
1.完成木马反检测技术 (更精细化和更强的迷惑性)，如**API混淆**：XOR编码函数名，动态解析NT API、**反沙箱检测**：性能、屏幕分辨率、磁盘检查、**行为混淆**：随机延迟和“合法操作”。
2.搭建gemini-balance apikey负载均衡（用roocode编程），并解决上述编程过程中遇到如干问题。
下周
1.继续研究木马动态免杀方面
2.测试badusb硬件

---

主线任务
1.badusb输入法切换问题
2.显示先具有“演示”意义攻击性的木马，如界面弹窗、炸弹等等，要求视觉上的尽可能的体验
3.分享ai使用心得及其相关的工具

---

本周：
1.烧录badusb程序，并进行了相关的测试，目前存在一些输入法的问题，正在解决
2.研究木马免杀相关，如内存混淆、加解密、模块践踏、动态代码洞扫描逻辑
3.制作钓鱼内容、文案、公章等，生成相关pdf
下周：
1.突破badusb的输入法相关的限制，或者尝试其他更优的近源攻击手段
2.内网穿透，将木马下载地址映射出公网

---

本周：
1.尝试并对比了两种常见的内网穿透方法，方案一为ngrok（其有静态域名，且40mbps的带宽）；方案二为花生壳（带宽只有1mbps，也有静态域名）；
2.尝试新的近源攻击设备USBAirborne，目前正在分析解决驱动器无法执行exe的问题；
3.调研国内的头部加密卡厂商；
下周：
1.接着继续对ngrok结合木马payload进行测试；
2.继续解决USBAirborne的相关无法执行exe的问题；

相关调研prompt：
请以提供的上述的“《2025 中国网络安全加解密卡市场与技术深度研究报告》（最终版）”为基础，参考以下材料内容进行补充，整合提炼形成一份内容更全面、论证更充分、细节更丰富的‘最终版本’调研报告。且新增补的内容必须与上述的“《2025 中国网络安全加解密卡市场与技术深度研究报告》（最终版）”的上下文逻辑紧密衔接，确保行文流畅、过渡自然。必要时，可对‘最佳版本’的段落结构进行微调，以容纳新增信息，添加内容前需要对内容进行相关的信息核对，确保其正确性及权威性，并将整合后的版本命名为（最终版v1）。

---

* [ ] 未来需要优化的如augment提示词、codelf mcp等
* [ ] https://www.educoder.net/competitions/jyxcds2/list  新的比赛

本周：
1.已经基本完成rust beacon免杀，目前能够远程命令，远程截图和远程vnc有点问题，需要进行优化；
2.完成“2025数据要素比赛”ppt，以及项目书；
3.完成了“《2025 中国网络安全加解密卡市场与技术深度研究报告》”“《2025加密卡主流厂商产品分析》”“《国密三级加密卡产品调研》”的相关内容调研：
下周：
1.对免杀细节进行修复：
2.寻找调研“2025第二届教育信息技术应用创新大赛”思路和方案；
3.继续跟进badusb方案


---

本周：
1.对“2025第二届教育信息技术应用创新大赛”进行相关的调研分析，并结合我们的目前项目案例，发现我们缺乏教育领域的实际应用案例，不符合参赛标准；
2.针对beacon免杀进行优化，问题出在截图时，远程注入进程被360发现，考虑手写beacon；
下周：
1.考虑根据现有框架功能，进行需求beacon的设计
2.进一步的badusb测试分析，针对.inf的配置文件进行案例测试
