su root  (进入root模式)          Ctrl + D(退出root模式)

sudo apt install -y open-vm-tools open-vm-tools-desktop (允许系统和主机之间进行粘贴复制)


sudo apt -get update (安装包时出现问题，就用sudo apt clean清除本地软件包缓存）


pyenv activate my_env (激活环境)
pyenv deactivate (退出环境)


python --version  # 查看Python版本
which python      # 查看Python可执行文件路径
python -c "import sys; print(sys.prefix)"  # 查看Python安装路径
python -c "import site; print(site.getsitepackages())"  # 查看全局包安装路径