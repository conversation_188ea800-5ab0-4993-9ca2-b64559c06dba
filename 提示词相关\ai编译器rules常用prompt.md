

# eg：1

`<Objective>`

Formalize the plan for next steps using sequentialthinking, taskmanager, context7 mcp servers and your suite of tools, including agentic task management, context compression with delegation, batch abstractions and routines/subroutines that incorporate a variety of the tools. This will ensure you are maximally productive and maintain high throughput on the remaining edits, any research to contextualize gaps in your understanding as you finish those remaining edits, and all real, production grade code required for our build, such that we meet our original goals of a radically simple and intuitive user experience that is deeply interpretable to non technical and technical audiences alike.

We will take inspiration from the CLI claude code tool and environment through which we are currently interfacing in this very chat and directory - where you are building /zero for us with full evolutionary and self improving capabilities, and slash commands, natural language requests, full multi-agent orchestration. Your solution will capture all of /zero's evolutionary traits and manifest the full range of combinatorics and novel mathematics that /zero has invented. The result will be a cohered interaction net driven agentic system which exhibits geometric evolution.

</Objective>

<InitialTasks>

To start, read the docs thoroughly and establish your baseline understanding. List all areas where you're unclear.

Then think about and reason through the optimal tool calls, agents to deploy, and tasks/todos for each area, breaking down each into atomically decomposed MECE phase(s) and steps, allowing autonomous execution through all operations.

</InitialTasks>

<Methodology>

Focus on ensuring you are adding reminders and steps to research and understand the latest information from web search, parallel web search (very useful), and parallel agentic execution where possible.

Focus on all methods available to you, and all permutations of those methods and tools that yield highly efficient and state-of-the-art performance from you as you develop and finalize /zero.

REMEMBER: You also have mcpserver-openrouterai with which you can run chat completions against :online tagged models, serving as secondary task agents especially for web and deep research capabilities.

Be meticulous in your instructions and ensure all task agents have the full context and edge cases for each task.

Create instructions on how to rapidly iterate and allow Rust to inform you on what issues are occurring and where. The key is to make the tasks digestible and keep context only minimally filled across all tasks, jobs, and agents.

The ideal plan allows for this level of MECE context compression, since each "system" of operations that you dispatch as a batch or routine or task agent / set of agents should be self-contained and self-sufficient. All agents must operate with max context available for their specific assigned tasks, and optimal coherence through the entirety of their tasks, autonomously.

An interesting idea to consider is to use affine type checks as an echo to continuously observe the externalization of your thoughts, and reason over what the compiler tells you about what you know, what you don't know, what you did wrong, why it was wrong, and how to optimally fix it.

</Methodology>

<Commitment>

To start, review all of the above thoroughly and state "I UNDERSTAND" if and only if you resonate with all instructions and requirements fully, and commit to maintaining the highest standard in production grade, no bullshit, unmocked/unsimulated/unsimplified real working and state of the art code as evidenced by my latest research. You will find the singularity across all esoteric concepts we have studied and proved out. The end result **must** be our evolutionary agent /zero at the intersection of all bleeding edge areas of discovery that we understand, from interaction nets to UTOPIA OS and ATOMIC agencies.

Ensure your solution packaged up in a beautiful, elegant, simplistic, and intuitive wrapper that is interpretable and highly usable with high throughput via slash commands for all users whether technical or non-technical, given the natural language support, thoughtful commands, and robust/reliable implementation, inspired by the simplicity and elegance of this very environment (Claude Code CLI tool by anthropic) where you Claude are working with me (/zero) on the next gen scaffold of our own interface.

Remember -> this is a finalization exercise, not a refactoring exercise.

</Commitment>

claude ultrathink

---
