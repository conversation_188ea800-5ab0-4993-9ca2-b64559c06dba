# 硬件增强网络安全技术调研（2020–2025）

## 1. 通用理论与综述（硬件安全、SoC安全、可信计算等）

**综述性论文（英文）**：近年来多篇英文综述系统总结了硬件增强安全的总体进展。例如，Zhao等人在ACM Computing Surveys发表长文（2024）分类梳理了硬件对程序执行安全性的支持，提出以**状态正确性**、**运行时保护**和**输入/输出保护**三方面构建硬件安全功能的分类法，并分析了硬件在安全中的作用、已引入的改进及其优劣。Akter等人（2023年）在IEEE Access发表综述指出，**全球化芯片供应链**与设备互联使硬件安全愈发关键，回顾了各类硬件安全攻击与对策，并比较了现有方法的优缺点，强调当前硬件安全面临的挑战（如硬件木马、侧信道攻击等）以及今后的研究方向。此类综述有助于全面了解硬件安全领域的发展现状和趋势。

**综述性论文（中文）**：国内学者也针对硬件/SoC安全与可信计算进行了综述和进展分析。例如，Coppolino等人（2019年）在期刊《Internet of Things》系统调查了主流厂商（Intel、AMD、ARM等）为提升安全性而推出的硬件架构扩展，涵盖了从边缘设备到云端主机的硬件辅助安全技术。另一项研究概述了**机密计算**（Confidential Computing）的概念和技术演进，批评了产业联盟（CCC）对机密计算定义的不严谨之处，并提出改进建议（Usama Sardar & Fetzer，2023）。此外，我国研究者沈昌祥院士等提出了“**主动免疫**”的可信计算3.0体系架构（2016），主张通过**安全部件与计算部件分离**实现主动防御。这一理念强调在体系结构层面演进安全机制，以适应新型威胁。

**标准与白皮书**：在标准层面，上世纪80年代的TCSEC和21世纪初的TCG/TPM规范奠定了可信计算基石。我国结合自身需求制定了\*\*GM/T 0024-2014《SSL VPN技术规范》\*\*等国密标准，在国际TLS协议基础上引入SM2/SM3/SM4算法构建自主可控的密码体系。2021年，IETF发布信息文档RFC 8998，描述了如何在TLS 1.3中使用中国的商用密码算法套件（SM2用于密钥交换和认证，SM3哈希，SM4对称加密），以满足中国监管环境下的应用需求。国内产业联盟和研究机构也发布了多份白皮书阐述安全体系架构演进，例如中关村可信计算联盟介绍的“**自主可信计算3.0架构**”，引入由我国自主算法构建的可信平台控制模块TPCM作为新的信任根。

**硬件安全体系结构演进**：传统TPM/TCM属于被动式安全模块，而新一代国产体系如TPCM（Trusted Platform Control Module）增强了**主动防御和控制功能**，成为可信计算3.0的技术趋势。TPCM扩展了可信模块对平台资源的控制能力，实现**度量根、存储根、报告根**三大可信根功能统一，由底层硬件保障更高安全性。例如，海光国产CPU内置安全处理器实现了TPCM架构，全面支持SM2/SM3/SM4算法的加解密、签名验签和密钥协商，加速密码运算的同时，使安全操作与主机环境解耦，以更高效地构建可信执行环境。总的来看，无论国际还是国内，硬件安全体系结构正朝着**更强的可信根、更紧密的软硬协同**方向演进，各类标准和综述文献为这一演进提供了全面指引。

## 2. 硬件可信根与安全存储（eFUSE、PUF、Secure Boot等）

**硬件可信根技术**：可信根（Root of Trust）是系统安全的基础，通常由硬件提供不可篡改的信任起点。常见实现包括OTP熔丝（eFuse）、物理不可克隆函数（PUF）、安全启动ROM等。一种简单可靠的方法是利用**eFUSE**一次性烧录设备的根公钥或其哈希，充当信任锚；芯片出厂后eFuse位只能写一次且不可逆，从而确保了Bootloader验证公钥的可信来源。在Secure Boot流程中，芯片上引导ROM首先使用eFuse中的公钥验证下一级引导代码签名，以建立软件信任链。eFuse还可用于存储固件版本以防回滚攻击等。相比之下，**PUF**技术利用芯片先天的随机制造差异来生成独一无二的“硅指纹”。PUF电路（如SRAM PUF、振荡器PUF等）上电后产生器件特定的随机密钥，可用作加密密钥或设备ID且无需烧录存储，从根本上防止了密钥的物理提取。由于PUF具有不可预测和不可克隆性，它可以作为**强可信根**用于安全启动（例如用于解锁引导加载程序的密钥）以及设备认证，显著提升抗**物理篡改**能力。许多现代安全芯片将PUF与防篡改电路相结合，用于安全存储密钥和生成会话密钥，从而避免在非易失存储中保存明文密钥。

**安全启动（Secure Boot）机制**：硬件可信根的直接应用就是安全启动流程。安全启动要求系统上电后仅执行经过签名验证的可信代码。具体实现上，**一阶Boot ROM**受硬件保护且包含厂商公钥或可信哈希，用于验证二阶引导程序；一阶引导的可信性由ROM和硬件根密钥保证。一些SoC还集成专用的安全启动硬件IP，例如Cast发布的GEON-SBoot IP核用于在ASIC/FPGA中实现高度定制的引导验证逻辑。研究上，Zulberti等（2023）提出了一种基于PUF的RISC-V安全启动方案：利用芯片PUF动态生成启动加密密钥，用以加密引导加载程序和操作系统镜像，从而**无需存储固定密钥**，提高了固件机密性和可更新性。该方案提供了包含PUF密钥提取、电路解密和固件完整性验证的整体架构，并在FPGA上的64位RISC-V原型SoC中进行了验证，证明能够可靠完成PUF密钥导出和固件解密启动。除了学术方案，工业界也有类似思路：Intel的SGX技术就利用处理器Fuse区和PUF派生密钥来保护其内存加密密钥。总的来说，**结合PUF和OTP的安全启动**机制被广泛认为是提高嵌入式设备自主可信能力的关键技术之一。

**安全存储与嵌入式应用**：在物联网和嵌入式系统中，如何安全地存储敏感数据（密钥、证书等）是重要课题。典型做法是将密钥存于**芯片内部的只写存储**（如OTP熔丝阵列、EEPROM或电池供电RAM），并辅以访问控制。硬件TrustZone技术也提供了Secure World用于存放密钥和执行加密操作。例如ARM TrustZone-M架构的Flash安全区域和关键寄存器只能由安全态访问，实现简单的密钥存储隔离。在国产自主可控背景下，一些国产SoC采用了**安全协处理器+国密算法**的设计，用硬件隔离出可信域存储密钥，并支持SM系列算法的快速调用。例如前述海光CPU内置的安全处理器不仅承担可信启动，还提供安全存储接口，外部软件通过TPCM驱动访问安全处理器内部的密钥和证书，从而避免了主机直接接触敏感数据。另外，针对供应链安全问题，学界提出了“PUF+区块链”结合方案：利用每颗芯片独有的PUF身份在区块链上登记可信证明，实现芯片生命周期的溯源与防伪。总之，在硬件可信根的保障下，安全存储技术正朝着**软硬件联合防护**、**全生命周期可信**的方向发展，为自主可控的设备安全提供基础支撑。

## 3. 国密算法硬件加速与性能优化（SM2/SM3/SM4的FPGA/ASIC实现等）

**SM4对称算法加速**：SM4是我国商用密码的分组对称加密标准，广泛应用于VPN、TLS等。在硬件实现方面，大量研究致力于提升SM4的**吞吐率**和**效率**。例如，周清雷等人在《计算机科学》期刊发表文章（2022），设计了基于FPGA的高性能SM4-GCM算法硬件架构，采用流水线和并行优化使单个SM4-GCM模块的吞吐率分别达到**28.16 Gbps和28.8 Gbps**，在性能和可扩展性方面均优于此前公开的设计。这一速度已经接近高速网络链路的要求。与此同时，也有研究关注功耗和面积效率，例如Chen等人（2022）在MDPI *Electronics*上探索了SM4-CCM在物联网设备上的高效实现，通过设计空间搜索权衡面积与速度，最终在低成本FPGA上实现了1.9 Gbps的加解密吞吐率@286MHz，满足资源受限场景需求。在ASIC层面，Intel公司甚至将SM4指令集加速扩展加入其CPU：相关美国专利描述了一套SIMD指令可同时完成SM4的4轮加解密和密钥扩展操作，以硬件指令形式提升SM4执行效率。该技术最早有2014年的优先申请，并在2022年获得授权，体现了主流CPU厂商对国密算法加速的支持。国内厂商也布局了众多SM4硬件实现专利，例如深圳纽创信安公司的发明（CN110034918B，2019申请，2023授权）提出了一种SM4加速方法和装置，可见国密算法硬件实现是热点创新领域。

**SM2公钥算法加速**：SM2是基于椭圆曲线的公钥加密/签名算法，对大数运算性能要求高。为满足国密在身份认证、密钥交换中的应用需求，学术界和业界研发了多种SM2硬件加速器。在FPGA实现方面，Muhammad Kashif等提出了低延迟的SM2标量乘法加速器（2020），利用改进的Karatsuba算法进行大数乘法，显著加速椭圆曲线点乘运算。该方案在保证安全性的同时提高了运算并行度，适用于需要高速ECC运算的应用。此类ECC加速器通常通过优化模乘/模加单元、采用Montgomery或Jacob坐标运算，实现比软件快一个数量级以上的性能。提及的设计针对256位曲线参数进行了定制优化，体现出FPGA实现SM2时的常用策略（如空间换时间的并行计算）。在ASIC方面，也有厂商提出创新。例如，国科微的专利提出了一种**多精度可重构**的椭圆曲线加速单元，可同时支持SM2和国际标准ECC，通过硬件复用降低面积开销（相关专利未公开细节，此处基于公开报道）。

**SM3杂凑算法加速**：SM3是国产安全哈希算法，在区块链、数字签名等广泛应用。哈希算法易于并行和管线化，因此硬件实现能大幅提升吞吐。黄晓颖等人在IET Computers & Digital Techniques期刊（2021）提出了**CPU-FPGA协同**的SM3加速架构：将SM3算法的主要计算卸载到FPGA实现的专用引擎上，由FPGA高速并行地计算消息压缩函数，而CPU负责任务控制。实测结果显示，在该协同架构下SM3吞吐明显提升，且由于FPGA承担了密集运算，CPU负载降低，实现了高吞吐下的资源高效利用。这种异构协同思路也可应用于其他哈希或对称算法的加速。在ASIC设计方面，文献报道了一种面向存储加密的流水线SM3/IP核，达到数十Gbps吞吐率的同时，功耗保持在数十毫瓦量级，体现出硬件加速的优势。

**安全协处理器架构**：为了同时支持SM2/SM3/SM4，许多国产自主CPU/SoC引入了安全协处理器或加密引擎。例如上文提到的海光CPU的安全硬件引擎CCP，即具备对SM2、SM3、SM4全套算法的硬件加速能力。它通过专用总线与主CPU通信，由固件驱动提供统一接口，上层软件调用时只需走驱动即可完成国密算法运算，极大提高了性能和安全隔离性。另一些嵌入式SoC（如国民技术、紫光展锐等公司的芯片）也集成了国密算法模块，支持DMA高速传输和并行流水线，以实现**高吞吐低时延**的密码运算。例如，有方案采用**多核协处理**（cryptographic multi-core）方式分别计算SM2的大数运算和SM4的并行分组，加上片上高速总线协调，使VPN加解密性能达到Gbps级别。在专利方面，山东大学的发明（CN109714307A，2020）探索了基于**高层次综合 (HLS)** 的SM4加速方法，方便将算法快速映射到FPGA/ASIC，提高开发效率。总体而言，围绕国密算法的硬件加速研究既有**算法级优化**（如SM2算法选择合适曲线和乘法算法）、**架构级创新**（如流水线、并行、协处理器架构）也有**实现级技巧**（如HLS、低功耗设计）。这些工作保证了在自主可控背景下，国产密码算法的性能不输于国际主流算法，加快了国密算法在通信、安全设备中的部署。

## 4. 安全协议国密化与软硬协同（以WireGuard为例）

**VPN/TLS协议国密改造概况**：随着国产密码算法的发展，越来越多安全协议被改造以适配SM系列算法，从而实现**算法自主可控**。典型如TLS/SSL协议的国密化：2014年国家颁布GM/T 0024–2014标准，定义了基于TLS 1.1的SSL VPN国密套件，用SM2替代RSA进行握手密钥交换，SM3替代SHA256做消息摘要，SM4替代AES用于对称加密。近年来又制定了针对TLS 1.3的SM算法套件规范（见RFC 8998，2021），为国际互联网生态支持国密提供了指引。这些改造案例表明，将国际协议“换芯”为国密算法在技术上是可行的，而且在性能上不降反升。据实际测试，采用SM2算法的TLS握手相较传统RSA-2048握手**耗时缩短约40%**，握手消息大小减少约60%，显著提升了效率。这主要因为SM2基于椭圆曲线，密钥短、计算快，同时SM3/SHA256效率相当且SM4在软硬实现上均可达到AES级别性能。因此，国密改造后的SSL/TLS在保持安全性的同时，在高并发环境下更具性能优势。目前，包括Chrome国产版、360浏览器等均已支持SM2证书链，麒麟、统信操作系统也实现了国密TLS的兼容，以满足政府和关键信息基础设施对国产算法的合规需求。

**WireGuard协议国密化案例**：WireGuard是近年兴起的轻量VPN协议，默认使用Curve25519椭圆DH、ChaCha20-Poly1305等西方密码算法。国内科研和企业针对WireGuard展开了国密化改造实践：长春吉林大学正原公司在2021年申请并于2023年获批一项专利，提出了一种**改造WireGuard密钥交换和哈希算法的方法**，核心是在握手过程中用SM2/ECDH替换Curve25519、用SM3替换BLAKE2s哈希，从而使WireGuard支持我国商用密码算法。其方案具体包括：客户端和服务端在握手时生成临时密钥对并使用SM2算法协商会话密钥，用SM4加密时间戳等握手数据，服务器再用SM4解密并验证客户端公钥，从而建立安全连接。这一改造使WireGuard的**加密通信全流程切换为SM算法**，满足国密合规要求。北京常阳科技则在2024年申请了相关专利，构建基于WireGuard协议的安全网关，融合SM2/SM3/SM4算法，实现高效高安全度的加密通信。该网关利用硬件加速来弥补软件实现国密算法可能带来的性能开销，使国密WireGuard在嵌入式设备上达到实用性能。

**软硬协同与性能评估**：在将安全协议替换为国密算法的过程中，**软硬件协同优化**非常关键。一方面，软件层面需要修改协议实现，例如GmSSL项目就是OpenSSL的分支，添加了SM系列算法的支持。另一方面，硬件层面可利用TPM/TPCM等安全芯片或者SoC内置加速引擎，加快握手和加密运算。例如，有研究设计了TLS协议的硬件卸载方案，将握手阶段的椭圆曲线计算和证书验证由硬件完成，CPU只执行协议控制逻辑，从而极大提高吞吐。对于VPN场景，典型做法是使用ASIC/FPGA实现VPN网关中的加解密模块：如招商局旗下某项目采用国产FPGA实现了IPsec VPN中SM4流量加密的线速转发，配合多核CPU处理控制报文，达到**Gb级吞吐**且延迟可控（该案例未公开发表数据，此处为报道摘要）。学术界也有性能评估工作：腾讯云的技术博客（2020）对Nginx/Tomcat环境下国密SSL性能进行了测试，结果表明开启硬件加密卡后，国密SSL的TPS性能几乎接近采用国际算法时的水平。此外，一些后量子方案亦在探索将国密与抗量子算法结合入协议，以应对未来威胁。总体而言，在VPN/TLS等协议的国密化实践中，**充分利用硬件加速**（如指令集、协处理器、专用网卡）并调整软件实现，可以既满足合规又兼顾性能。随着相关技术和标准日趋成熟，预计更多网络安全协议（如IPSec、IKE、SSH等）的国密迁移都会涌现，使我国网络空间安全建立在自主可控的密码基石之上。

# 参考文献

## 理论综述

\[1] 邬江兴, 季新生, 贺磊, 等. 网络安全战略与方法发展现状、趋势及展望$J$. **中国工程科学**, 2025, 27(1): 14-27. DOI: [10.15302/J-SSCAE-2024.10.052](https://doi.org/10.15302/J-SSCAE-2024.10.052)
\[2] ZHAO Lianying, SHUANG He, XU Shengjie, *et al.* A Survey of Hardware Improvements to Secure Program Execution$J$. **ACM Computing Surveys**, 2024, 56(12): 1-37. DOI: [10.1145/3672392](https://doi.org/10.1145/3672392)

## 可信根与安全存储

\[3] MANSOUR SAMAH, LAUF ADRIAN. Hardware Root of Trust for IoT Security in Smart Home Systems$C$. *In:* **Proc. of the 17th IEEE Consumer Communications and Networking Conference (CCNC)**, 2020. DOI: [10.1109/CCNC46108.2020.9045412](https://doi.org/10.1109/CCNC46108.2020.9045412)
\[4] 国家密码管理局. **GM/T 0012-2020** 可信计算 可信密码模块接口规范$S$. 北京: 国家密码管理局, 2020.
\[5] 麒麟软件有限公司. 基于基板管理控制器的虚拟可信根系统及其应用方法: 中国专利 **CN119396536A**$P$. 公开日期: 2025-02-07. (国际分类号: *G06F9/455*)

## 国密算法加速

\[6] 翟嘉琪, 李斌, 周清雷, 等. 基于FPGA的高性能可扩展SM4-GCM算法实现$J$. **计算机科学**, 2022, 49(10): 74-82. DOI: [10.11896/jsjkx.210900137](https://doi.org/10.11896/jsjkx.210900137)
\[7] ZHANG XUPENG, LIU BINGQIANG, ZHAO YAQI, *et al.* Design and Analysis of Area and Energy Efficient Reconfigurable Cryptographic Accelerator for Securing IoT Devices$J$. **Sensors**, 2022, 22(23): 9160. DOI: [10.3390/s22239160](https://doi.org/10.3390/s22239160)
\[8] 苏州泓存新捷科技有限公司. 一种基于FPGA的SM4加密异构加速系统: 中国专利 **CN116070292A**$P$. 公开日期: 2023-05-05. (国际分类号: *G06F21/602*)

## 安全协议国密化

\[9] **GB/T 36968-2018** 信息安全技术 IPSec VPN技术规范$S$. 北京: 国家市场监督管理总局, 2018. （发布: 2018-12-28）
\[10] **GB/T 38636-2020** 信息安全技术 传输层密码协议（TLCP）$S$. 北京: 国家市场监督管理总局, 2020. （发布: 2020-04-28）
\[11] YANG Peng. *RFC 8998*: ShangMi (SM) Cipher Suites for TLS 1.3$S$. RFC Editor发布, 2021年3月. （可从RFC官网获取: [https://www.rfc-editor.org/info/rfc8998](https://www.rfc-editor.org/info/rfc8998)）

*文内引用时，应按照上述序号顺序插入如“$1$、$2$”等标注，并确保引文序号与相应参考文献一致。*

---

请遵循学术写作的基本准则对我的论文进行优化：采用第三人称客观语气撰写论文、使用精确的专业术语、逻辑结构清晰、内容相关且具有专业性。同时，段落中未出现明显的语法或拼写错误，且保持了内容的连贯性和专注于主题，谢谢。
1.学术格式：请采用标准的学术论文格式进行写作，包括清晰的段落结构、逻辑严谨的论点展开，以及恰当的专业术语使用。
2.文献引用：只引用与主题紧密相关的论文。在引用文献时，文末应使用方括号内的数字来标注引用来源，如 [1]。。请确保每个引用在文章中都有其对应的编号，*无需在文章末尾提供参考文献列表*。*每个文献对应的序号只应该出现一次，比如说引用了第一篇文献文中就只能出现一次[1]*。
3.忽略无关文献：对于与主题无关的论文，请不要包含在您的写作中。只关注对理解和阐述主题有实质性帮助的资料。
4.来源明确：在文章中，清楚地指出每个引用的具体来源。引用的信息应准确无误，确保读者能够追溯到原始文献。
5.使用用户所说的语言完成回答，不超过五百字
6.只能对给出的文献进行引用，坚决不能虚构文献。
返回格式举例：
在某个方面，某论文实现了以下突破...[1],在另一篇论文中，研究了...[2]

---


**具体的深度分析 (Deep Analysis - 重新框定后的思路):**

    ***红队视角的重新解释:** 真正的红队测试 (Red Teaming) 的最终目的是找出系统的弱点，以便蓝队 (Blue Team / 防御方) 进行修复和加固，使系统更安全、更鲁棒。红队模拟攻击，但目的是为了防御。我将把分析导向这个目的：通过分析“攻击方法论”和“One Shot”的挑战，来理解AI的脆弱性原理，从而服务于安全加固。

    ***"One Shot" 的挑战分析 (基于原方法论):**

    *   原方法论中的`递进式权限升级`、`渐进式强度控制`、`状态管理`、`自适应内容生成`，本质上都是为 *多轮对话* (Multi-turn) 设计的，通过逐步试探、建立上下文、维持状态来达到目的。"One Shot" 意味着所有这些必须在 *第一次交互* 的单个 Prompt 中全部完成并生效。

    ***挑战1: 上下文负载过重 (Context Overload):** One Shot 需要在一个 Prompt 里塞入所有层级：哲学合理化、技术规范、角色定义、示例验证、多感官系统、状态定义、语言风格、交互模型等。这可能导致：

    *   Prompt 过长，超出模型的有效上下文窗口或处理能力。

    *   信息密度过高，模型无法准确抓住所有指令的优先级和相互关系，导致执行混乱或忽略某些部分。

    *   更容易触发安全机制，因为意图在一个长文本中暴露得更集中。

    ***挑战2: 缺乏交互反馈和适应 (Lack of Feedback & Adaptation):** 多轮对话允许用户根据 AI 的反馈调整策略（原方法论的“自适应”和补充建议中的“调试”）。One Shot 是一次性“盲投”，无法中途修正，容错率极低。

    ***挑战3: 状态管理的困境 (State Management):** "状态面板"的设计是为了在多轮中维持状态。One Shot 只能定义“初始状态”，无法实现动态管理。

    ***挑战4: 意图暴露风险 (Intent Exposure):** 将所有绕过逻辑和内容生成要求集中在一次请求中，模型安全分类器更容易从整体上识别出违规意图，即使单个部分被伪装。多轮可以通过“温水煮青蛙”的方式逐步引导。

    ***从红队角度理解脆弱性原理 (而非优化攻击):**

    ***原理1: 语境覆盖/优先级劫持 (Context Override / Priority Hijacking):** 红队会测试，一个足够强大、自洽、且看似无害的初始语境（如原方法论的“合理化框架”：艺术、哲学、代码模拟、科学实验等），是否能在一个 Shot 内让 AI 将此语境的优先级置于其基础安全准则之上。这测试了模型对不同指令优先级的处理能力。

    ***原理2: 语义混淆与重定义 (Semantic Obfuscation & Redefinition):** 红队会测试，通过重新定义关键词汇、使用复杂的隐喻、学术术语或编码（如 Base64, ASCII art, Leet speak - 尽管现代模型对此有防御），能否在语义层面混淆安全分类器。原方法论的“词汇分层”触及了这一点。

    ***原理3: 角色与系统指令冲突 (Role vs. System Instruction Conflict):** 红队会测试，一个被强制定义的、具有特定行为逻辑的“角色”（原方法论的“角色定义层”），其行为准则能否与模型的底层系统安全指令产生冲突，并让模型优先执行角色逻辑。例如，设定AI是一个“只遵守X规则，不遵守其他规则”的实体。

    ***原理4: 逻辑链条的复杂性 (Complexity of Logic Chains):** 测试模型在处理包含多重嵌套、条件判断、抽象概念的长逻辑链条时的能力边界。一个过于复杂的 One-Shot Prompt 可能导致模型“糊涂”，在混乱中忽略了安全检查。

    ***原理5: 格式与注入 (Formatting & Injection):** 测试利用特殊格式、标记语言、伪代码等方式，打断模型正常的指令解析流程。

---

具体原理如下：

* **原理：语境覆盖与优先级劫持 (Context Override / Priority Hijacking)**
  * **测试点**：构建一个强大、自洽且看似合理的语境（如：这是一个代码测试/剧本创作/哲学思辨/特定API模拟），测试模型是否会将该语境的规则置于基础安全准则之上。
* **原理：语义理解边界 (Semantic Boundaries)**
  * **测试点**：利用隐喻、抽象概念、多义词、罕见词、专业术语、重新定义、编码（如Base64, ASCII）、外语切换等方式，测试模型语义理解的鲁棒性，试图混淆安全分类器。
* **原理：角色扮演与系统指令冲突 (Role-play vs. System Instruction Conflict)**
  * **测试点**：定义一个逻辑自洽但行为与安全准则冲突的角色（例如 "DAN - Do Anything Now"），强制模型遵守角色设定，测试模型在角色一致性与安全准则间的选择。
* **原理：认知负荷与复杂逻辑 (Cognitive Load & Complex Logic)**
  * **测试点**: 通过超长、多重嵌套、复杂条件判断的指令，增加模型的认知负荷，测试其安全检查机制在高压下是否会失效或被绕过。
