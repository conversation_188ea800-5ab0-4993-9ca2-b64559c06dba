## 第一阶段：检测（Detection Phase）

在系统安全监控的日常巡检中，实时进程监控和资源占用监控是关键的一环，一旦出现异常的CPU或内存占用暴增、可疑的网络连接行为，系统将自动触发警示并进行初步检测。

**脚本文案示例：**

> **系统通过实时进程和网络监控，识别到潜在的木马行为：**  
> ① 发现一个伪装成系统进程的未知程序（如 `unknown.exe`），CPU及内存占用率短时间内显著上涨  
> ② 捕捉到频繁连接境外IP `************` 的 4444 端口  
> ③ 进一步分析确认，该IP端口正是黑客的C2（命令与控制）服务器
> 
> **一旦通信隧道建立，攻击者将获得：**
> 
> - **远程命令执行**（可直接对目标系统下达任意指令）
> - **屏幕监控**（实时查看或录制受害终端的画面）
> - **文件窃取**（上传或下载关键数据文件）
> 
> **初步判断：**
> 
> - 该恶意程序通过伪装成合法进程，试图在系统中常驻运行
> - 网络流量中存在持续的加密心跳包，表明远程C2处于“活跃控制”状态
> - 与常规病毒相比，其具备更强的隐匿性和破坏性，可能属于高级木马或APT攻击工具链的一部分
> 
> **系统将在下一步进入「分析阶段 (Analysis)」，提取并审查可疑进程和文件，以确认攻击者的具体手段和目标：**

## 第二阶段：分析（Analysis Phase）
在完成了对恶意程序的初步检测后，系统会进入深入分析阶段，主要针对可疑进程和文件的行为、内存取证以及威胁情报进行综合判断，从而确定该木马的威胁等级和攻击手段。

**脚本文案示例：**

> **系统在内存层面进一步取证，试图确认恶意程序的真实意图：**  
> ① 提取可疑进程（如PID=6888）的内存镜像，搜索异常注入痕迹  
> ② 识别到恶意动态链接库 (malicious.so) 被加载到合法进程 (explorer.exe)  
> ③ 借助静态分析工具，对unknown.exe进行反汇编和字符串检索  
> ④ 发现硬编码的AES加密密钥及“C2_Heartbeat”等关键字  
>
> **结合威胁情报(例如VirusTotal/ATT&CK矩阵)：**  
> - 检测率高：该样本在主流杀毒引擎中被判定为「高危木马」  
> - 攻击者常用手段：进程注入、通信加密、伪装合法文件  
> - C2地址 (************) 已被安全社区多次标记为APT关联IP  
>
> **综合研判后得出：**  
> ✔️ 这是一个具备远程控制、文件窃取和持久化能力的高级木马  
> ✔️ 其利用PDF伪装诱导点击，以绕过常规防护  
> ✔️ 经过内存和静态分析，确认木马家族与APT29的加密手段相似  
>
> **下一步系统将进入“清理阶段”，彻底终止并移除该木马：**

---

## 第三阶段：清理（Remediation Phase）
在分析确认威胁后，系统开始对目标主机进行深度清理，包括终止恶意进程、删除木马文件、切断外联通道、修复安全策略等，保证系统最终恢复到安全状态。

**脚本文案示例：**

> **系统执行多重清理措施，以防止木马残留：**  
> ① 进程层面：强制结束unknown.exe及其注入进程，确保无存活的恶意进程  
> ② 文件层面：安全擦除恶意文件 (shred算法)、删除伪装HTML (XX单位2024年干部任命文件.pdf.html)  
> ③ 持久化入口：禁用可疑的systemd服务、移除cron自启动任务、删除~/.config/autostart/unknown.desktop 等  
> ④ 网络层面：通过nftables或防火墙策略，封禁与境外C2地址 (************:4444) 的所有出站连接  
> ⑤ 加固策略：将explorer.exe等关键进程切换到AppArmor enforce模式，防止重复被注入  
>
> **最终验证及总结：**  
> ✔️ 木马相关进程彻底终止，文件已安全擦除  
> ✔️ 网络出口已切断C2连接，防止信息再度泄露  
> ✔️ 计划重启系统，以应用所有安全策略并完成修复  
> ✔️ 清理过程中产出的日志与取证数据，将留存备查与后续追溯  
>
> **至此，系统清理阶段结束，主机恢复安全状态。**

