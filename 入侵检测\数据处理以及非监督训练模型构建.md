# 第一部分：特征选择代码
```python
import pandas as pd
import numpy as np
from sklearn.feature_selection import VarianceThreshold
import logging
import os
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('feature_selection.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('FeatureSelector')

def feature_selection(data: pd.DataFrame, variance_threshold: float = 0.01, correlation_threshold: float = 0.9) -> pd.DataFrame:
    """
    对输入的数据进行特征选择，包括删除低方差特征和高度相关特征。

    Args:
        data: 输入的DataFrame数据，已完成预处理。
        variance_threshold: 方差阈值，低于该阈值的特征将被删除。
        correlation_threshold: 相关系数阈值，高于该阈值的特征将被删除。

    Returns:
        进行特征选择后的DataFrame。
    """
    logger.info("开始特征选择...")

    try:
        # 复制数据以避免修改原始数据
        features = data.copy()

        # 保存原始特征名
        original_feature_names = features.columns.tolist()
        logger.info(f"原始特征数量: {len(original_feature_names)}")

        # 1. 删除低方差特征
        selector = VarianceThreshold(threshold=variance_threshold)
        selector.fit(features)
        features_var = features.loc[:, selector.get_support()]
        logger.info(f"低方差特征过滤后剩余特征数量: {features_var.shape[1]}")

        # 2. 删除高度相关的特征
        corr_matrix = features_var.corr().abs()
        upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
        to_drop = [column for column in upper_tri.columns if any(upper_tri[column] > correlation_threshold)]
        features_selected = features_var.drop(columns=to_drop)
        logger.info(f"去除高度相关特征后剩余特征数量: {features_selected.shape[1]}")
        logger.info(f"最终选定的特征: {features_selected.columns.tolist()}")

        return features_selected

    except Exception as e:
        logger.error(f"特征选择发生错误: {str(e)}", exc_info=True)
        raise

def main():
    """
    主函数，执行特征选择并保存结果。
    """
    try:
        # 加载数据
        logger.info("开始加载数据...")
        data = pd.read_csv('your_preprocessed_data.csv')  # 请替换为实际的预处理后数据文件路径
        logger.info(f"数据加载完成，样本数量: {len(data)}")

        # 执行特征选择
        selected_features = feature_selection(data)

        # 保存选定的特征到 CSV 文件
        output_file = 'selected_features.csv'
        selected_features.to_csv(output_file, index=False)
        logger.info(f"选定的特征已保存到 {output_file}")

    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main()

```


在特征选择过程中，保留下来的这些特征通过了两个主要筛选标准：**低方差筛选**和**相关性筛选**。每个步骤的具体原因如下：

### 1. 低方差特征剔除
在数据分析中，低方差特征是那些在样本中几乎保持不变的特征。它们往往对模型贡献较少，甚至可能导致过拟合。我们通过方差阈值（0.01）来筛选掉低方差特征。这意味着保留下来的特征在样本间的变化较大，信息量较高，对区分样本可能更有价值。

在这个数据集的筛选过程中，有27个特征通过了低方差筛选，这表明这些特征在样本间存在足够的变动，具备一定的区分力。

### 2. 高相关特征剔除
高度相关的特征在机器学习建模中可能导致冗余和多重共线性问题，因此通常只保留其中一个。相关性阈值（0.9）设定为高相关性标准，这意味着若两个特征的相关系数超过0.9，则删除其中一个，以减少特征冗余和过拟合的风险。

在保留了27个高方差特征之后，我们对这些特征进行了相关性检查，剔除了高于0.9相关性阈值的冗余特征，最终剩下20个特征。这些保留下来的特征之间的相关性相对较低，因此更有助于模型从不同维度获取有效信息。

### 保留下来的特征分析

1. **unix_timestamp**：时间戳特征，可能有助于捕获时间相关的模式，尤其是在网络流量分析中，不同时段的流量特征可能不同。
2. **bytes_per_second_bin, bytes_per_second**：流量速率特征，表示在一定时间间隔内传输的数据量。对于网络分析，这类特征直接反映了流量的强度和变化趋势。
3. **avg_packet_size, min_packet_size, max_packet_size, packet_size_range**：包大小特征，提供了数据包传输的统计信息，可能揭示不同流量的大小特征，帮助识别异常流量或特定协议的使用。
4. **iat_mean, iat_std**：包到达间隔时间的均值和标准差，这些特征可以揭示网络流量的行为模式。某些网络攻击或恶意流量可能会表现出不同的到达时间间隔分布。
5. **payload_entropy**：负载的熵值，通常用于检测数据加密或压缩程度，可能有助于区分正常和异常流量。
6. **unique_dst_ratio**：唯一目标地址比率，表示会话过程中与不同目的地的连接比例，可以反映流量的多样性和分布情况。
7. **http_method_count_GET, http_method_count_POST**：HTTP请求方法统计，用于网络应用分析。不同应用程序或攻击行为会表现出不同的HTTP请求模式。
8. **url_length_mean**：URL长度的平均值，较长或随机的URL可能暗示异常流量或恶意行为。
9. **dns_query_type_count**：DNS查询类型的统计，用于识别DNS相关的网络流量模式，可能揭示恶意流量企图通过DNS进行数据传输。
10. **ssl_tls_version_count**：SSL/TLS协议版本统计，可能帮助识别加密流量中的特定协议版本。
11. **protocol_TCP, protocol_UDP**：协议类型特征，用于识别流量协议，可以显著区分不同网络行为。
12. **application_protocol_DNS, application_protocol_SSL_TLS**：应用协议特征，这些特征在流量中标识特定的应用层协议，有助于识别特定应用流量。

### 总结
这些保留下来的特征都是信息量较高且与其他特征相关性低的特征。它们在网络流量数据分析中具有很强的解释力，例如流量的大小、传输的频率、协议的分布等方面的信息，可以有效支持分类或异常检测任务。这种特征选择方式确保模型可以在没有冗余数据的情况下，利用多维度信息构建更精准的预测。


# 第二部分：训练和可视化代码
```python
import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import joblib
import logging
import os
from datetime import datetime
import warnings
import json
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('isolation_forest_training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('IsolationForestDetector')

class IsolationForestDetector:
    """
    基于隔离森林的网络流量异常检测器
    """
    def __init__(self, 
                 contamination: float = 0.05,
                 n_estimators: int = 200,
                 max_samples: str = 'auto',
                 random_state: int = 42,
                 model_dir: str = 'models'):
        """
        初始化检测器
        
        Args:
            contamination: 异常样本的预期比例
            n_estimators: 基估计器的数量
            max_samples: 用于训练每个基估计器的样本数
            random_state: 随机种子
            model_dir: 模型保存目录
        """
        self.model = IsolationForest(
            contamination=contamination,
            n_estimators=n_estimators,
            max_samples=max_samples,
            random_state=random_state,
            n_jobs=-1  # 使用所有CPU核心
        )
        self.scaler = StandardScaler()
        self.model_dir = model_dir
        self.feature_names = []
        self.training_stats = {}
        
        # 创建模型保存目录
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)
            
    def _prepare_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, list]:
        """
        准备数据，包括提取特征和标准化

        Args:
            data: 输入的DataFrame数据

        Returns:
            处理后的数据数组和特征名列表
        """
        logger.info("开始准备数据...")

        try:
            # 假设输入数据已经过特征选择
            features = data.copy()
            self.feature_names = features.columns.tolist()

            # 处理缺失值
            features = features.fillna(0)

            # 标准化特征
            scaled_features = self.scaler.fit_transform(features)

            logger.info(f"数据准备完成，特征维度: {scaled_features.shape}")
            return scaled_features, self.feature_names

        except Exception as e:
            logger.error(f"数据准备发生错误: {str(e)}", exc_info=True)
            raise
            
    def train(self, data: pd.DataFrame) -> None:
        """
        训练隔离森林模型
        
        Args:
            data: 训练数据DataFrame
        """
        try:
            logger.info("开始训练隔离森林模型...")
            start_time = datetime.now()
            
            # 准备数据
            X, feature_names = self._prepare_data(data)
            
            # 训练模型
            self.model.fit(X)
            
            # 计算训练集得分
            scores = self.model.decision_function(X)
            predictions = self.model.predict(X)
            
            # 记录训练统计信息
            self.training_stats = {
                'training_samples': len(X),
                'feature_count': len(feature_names),
                'anomaly_ratio': float((predictions == -1).sum()) / len(predictions),
                'score_mean': float(scores.mean()),
                'score_std': float(scores.std()),
                'training_time': str(datetime.now() - start_time)
            }
            
            logger.info(f"模型训练完成，训练统计信息: {json.dumps(self.training_stats, indent=2)}")
            
            # 保存训练可视化结果
            self._save_training_visualization(scores, predictions)
            
            # 保存模型和相关信息
            self._save_model()
            
        except Exception as e:
            logger.error(f"训练过程发生错误: {str(e)}", exc_info=True)
            raise
            
    def _save_training_visualization(self, scores: np.ndarray, predictions: np.ndarray) -> None:
        """
        保存训练结果可视化
        
        Args:
            scores: 异常得分
            predictions: 预测结果
        """
        try:
            # 创建可视化目录
            viz_dir = os.path.join(self.model_dir, 'visualizations')
            if not os.path.exists(viz_dir):
                os.makedirs(viz_dir)
            
            # 绘制异常得分分布图
            plt.figure(figsize=(10, 6))
            sns.histplot(scores, bins=50, kde=True)
            plt.title('异常得分分布')
            plt.xlabel('异常得分')
            plt.ylabel('频数')
            plt.savefig(os.path.join(viz_dir, 'anomaly_scores_distribution.png'))
            plt.close()
            
            logger.info(f"训练可视化结果已保存至: {viz_dir}")
            
        except Exception as e:
            logger.warning(f"保存可视化结果时发生错误: {str(e)}", exc_info=True)
            
    def _save_model(self) -> None:
        """
        保存模型和相关信息
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存模型
            model_path = os.path.join(self.model_dir, f'isolation_forest_{timestamp}.joblib')
            joblib.dump({
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'training_stats': self.training_stats
            }, model_path)
            
            # 保存训练配置和统计信息
            config_path = os.path.join(self.model_dir, f'model_info_{timestamp}.json')
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'model_parameters': self.model.get_params(),
                    'feature_names': self.feature_names,
                    'training_stats': self.training_stats
                }, f, indent=2, ensure_ascii=False)
                
            logger.info(f"模型和配置信息已保存至: {self.model_dir}")
        except Exception as e:
            logger.error(f"保存模型时发生错误: {str(e)}", exc_info=True)
            raise
            
    def predict(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用训练好的模型进行预测
        
        Args:
            data: 输入数据DataFrame
            
        Returns:
            预测标签和异常得分
        """
        try:
            # 使用与训练时相同的特征
            features = data[self.feature_names].fillna(0)
            
            # 标准化
            scaled_features = self.scaler.transform(features)
            
            # 预测
            predictions = self.model.predict(scaled_features)
            scores = self.model.decision_function(scaled_features)
            
            return predictions, scores
            
        except Exception as e:
            logger.error(f"预测过程发生错误: {str(e)}", exc_info=True)
            raise

def main():
    """
    主函数
    """
    try:
        # 加载选定的特征数据
        logger.info("开始加载选定的特征数据...")
        data = pd.read_csv('selected_features.csv')  # 请确保此文件是第一部分生成的
        logger.info(f"数据加载完成，样本数量: {len(data)}")
        
        # 初始化检测器
        detector = IsolationForestDetector(
            contamination=0.05,  # 根据数据调整预期异常比例
            n_estimators=200,   # 增加树的数量提高模型稳定性
            max_samples='auto', # 每棵树的样本数
            random_state=42,    # 随机种子
            model_dir='network_anomaly_models'  # 模型保存目录
        )
        
        # 训练模型
        detector.train(data)
        
        # 进行预测（可选）
        predictions, scores = detector.predict(data)
        
        # 输出检测结果统计
        anomaly_ratio = float((predictions == -1).sum()) / len(predictions)
        logger.info("检测结果统计:")
        logger.info(f"- 总样本数: {len(predictions)}")
        logger.info(f"- 异常样本数: {(predictions == -1).sum()}")
        logger.info(f"- 异常比例: {anomaly_ratio:.2%}")
        logger.info(f"- 异常得分范围: [{scores.min():.3f}, {scores.max():.3f}]")
        
        # 可视化异常得分
        plt.figure(figsize=(10, 6))
        sns.histplot(scores, bins=50, kde=True)
        plt.title('预测集异常得分分布')
        plt.xlabel('异常得分')
        plt.ylabel('频数')
        plt.show()
        
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main()

```