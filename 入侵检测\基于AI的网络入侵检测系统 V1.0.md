# 基于AI的网络入侵检测系统 - 特征提取模块优化与实践（根据目前工作量进行阐述）

## 1. ==项目背景与动机==

随着网络攻击手段的日益复杂化,传统的基于规则和签名的入侵检测方法已难以有效应对新的安全威胁。为了提升入侵检测的智能化水平和实时响应能力,我们团队基于机器学习技术,开发了一套智能化的网络入侵检测系统。

在这个系统中,特征提取模块承担着至关重要的角色。它负责从海量、高维的网络流量数据中,自动学习和提取最能刻画流量模式的关键特征,为异常检测模型的训练提供高质量的输入。因此,优化特征提取模块的有效性和效率,成为提升整个入侵检测系统性能的关键举措。

本文重点介绍我们在网络流量特征工程领域开展的系列优化实践。通过迭代式的特征设计、分析、选择和优化,我们最终确定了一组13个关键特征,在异常流量检测的准确性、鲁棒性、实时性等方面取得了显著的提升。

## ==2. 特征工程流程概览==

我们的特征工程优化过程可以概括为以下几个关键步骤:

1. 围绕安全分析目标,设计初始的候选特征集。
2. 在真实流量数据上,计算各候选特征的统计分布。
3. 评估候选特征与攻击流量的相关性,初步筛除明显无效的特征。
4. 分析特征之间的相关性,识别并去除冗余特征。
5. 结合特征的统计性质、领域知识,构建高级复合特征。
6. 在攻击样本上评估特征的区分能力,优选关键特征子集。
7. 评估关键特征的计算效率,进行针对性的性能优化。
8. 将优选特征应用到异常检测模型,评估端到端的性能提升。
9. 分析现有方案的局限性,识别下一步的优化方向。

接下来,我们将逐一展开上述环节的技术实现细节。

## ==3. 关键特征集的设计与优选==

本项目最初设计的网络特征如下：

### **第一层：基础网络特征（计算成本低，实时分析）**

> 1. timestamp
> 2. src_ip
> 3. dst_ip
> 4. protocol
> 5. avg_packet_size
> 6. packets_per_second
> 7. port_entropy
> 8. protocol_entropy
> 9. ip_uniqueness_ratio
> 10. fragmentation_ratio
> 11. ttl_distribution
> 12. tcp_flags_distribution
> 13. inter_arrival_time
> 14. flow_duration
> 15. protocol_distribution

### **第二层：行为和上下文特征（中等计算成本，准实时分析）**

> 16. geo_location_anomaly
> 17. time_context_anomaly
> 18. protocol_port_correlation_anomaly
> 19. packet_header_consistency
> 20. connection_state_ratio
> 21. payload_entropy
> 22. traffic_trend
> 23. protocol_compliance
> 24. service_access_sequence_anomaly
> 25. user_role_deviation
> 26. ip_service_access_pattern_change
> 27. traffic_periodicity_index (合并 traffic_periodicity 和 seasonality_strength)

### **第三层：高级分析特征（高计算成本，批处理分析）**

> 28. local_outlier_score
> 29. change_point_score
> 30. entropy_ratio
> 31. protocol_transition_rate
> 32. tls_handshake_pattern
> 33. encrypted_traffic_rhythm
> 34. node_centrality
> 35. community_structure_change

但是考虑到特征冗余以及计算效率、实时性等，利用特征工程的方法对特征进行以下几种方法的优化：==**如特征分布分析结果分析、特征相关性分析（相关性矩阵热力图、PCA、VIF）、随机森林的特征重要性、SHAP值、Lasso、ElasticNet。**==

经过多轮迭代优化,我们最终确定了以下13个关键特征,作为刻画网络流量异常模式的最优特征集:

> 1. **dst_port**(目的端口号):反映了流量的目标服务类型,与攻击意图高度相关。
> 2. **bytes_per_second**(每秒字节数):刻画流量的密集程度,异常的峰值或波动常常预示攻击发生。
> 3. **packet_frequency**(数据包频率):表征流量传输的紧凑度,可以辨别持续或周期性的异常通信模式。
> 4. **flow_duration**(流持续时间):反映通信的持久性,可以区分正常业务访问和恶意连接的时间特征。
> 5. **avg_packet_size**(平均包大小):体现流量的有效载荷特征,某些攻击往往使用特定尺寸的数据包。
> 6. **Traffic_intensity**(流量强度):融合流密度和包大小的高级特征,全面刻画流量模式的异常水平。
> 7. **IAT_composite**(包间隔复合特征):结合包到达间隔均值和波动性的综合指标,反映流量传输的规律性。
> 8. **Upload_download_composite**(上传/下载复合特征):融合短期和长期上传下载比的异常检测指标。
> 9. **Payload_composite**(有效载荷复合特征):结合净荷大小和内容随机性,检测隐蔽信道或加密通信。
> 10. **TCP_flags**(TCP标志位):反映TCP连接的状态转换模式,可识别出扫描、DOS等异常行为。
> 11. **TCP_behavior**(TCP行为综合特征):结合TCP标志和状态机序列,全面刻画TCP异常通信模式。
> 12. **protocol_type_distribution**(协议类型分布):检测特定攻击倾向使用的异常协议模式。
> 13. **syn_fin_ratio**(SYN/FIN包比例):反映连接建立和关闭过程的异常,如SYN Flood、FIN Scan等。

这些特征覆盖了流量统计、连接模式、协议语义、载荷内容等多个关键维度,能够从不同角度检测各类常见和新型网络攻击的异常流量模式。

在特征优选过程中,我们综合运用了基于统计分布、特征相关性、领域知识的分析方法,同时兼顾特征的计算效率、存储成本、领域解释性等实用因素,力求用最小的特征集获得最优的检测性能。

## ==4. 结合深度包检测（DPI）特征==

**在现有特征集的基础上添加深度包检测（DPI）特征以增强入侵检测能力的分析**

---

为了进一步提升基于AI的入侵检测系统的性能，我们可以在现有特征集的基础上添加一些深度包检测（DPI）特征。DPI特征能够提供更深入的流量内容信息，有助于更准确地区分正常网络流量和攻击流量。

---

**一、理解深度包检测（DPI）特征**

**1.1 什么是DPI特征**

深度包检测（DPI）是一种网络分析技术，能够检查数据包的内容，包括应用层的数据。这与仅分析包头信息的浅层检测不同。DPI特征可以捕获网络流量的更细粒度信息，如应用层协议、命令、关键字、数据模式等。

**1.2 DPI特征在入侵检测中的作用**

- **检测应用层攻击**：许多攻击利用应用层协议的漏洞，DPI可以识别异常的应用层行为。
- **识别恶意内容**：通过检查数据包的负载，可以检测到恶意代码、特洛伊木马、病毒等。
- **检测协议滥用**：攻击者可能滥用常见协议（如HTTP、DNS）进行攻击，DPI能够识别不正常的协议使用方式。

---

**二、添加DPI特征的原则**

**2.1 避免特征冗余**

- **与现有特征的区分**：确保新增的DPI特征与现有特征不重复，提供新的信息维度。
- **考虑特征相关性**：在添加特征前，评估新特征与现有特征的相关性，避免高度相关。

**2.2 结合项目背景**

- **针对入侵检测需求**：选择能够有效区分正常和攻击流量的DPI特征。
- **实用性和可实现性**：确保特征在实际环境中可提取，并且不会过度增加计算和存储开销。

---

**三、建议添加的DPI特征**

**3.1 应用层协议识别**

- **特征描述**：识别流量所属的应用层协议，如HTTP、FTP、SMTP、DNS等。
- **作用**：
  - **检测异常协议使用**：识别在非标准端口上运行的协议或异常的协议使用方式。
  - **区分正常和攻击流量**：某些攻击可能使用特定的协议，如FTP用于传输恶意文件。
- **避免冗余**：
  - **与现有的 `protocol_*`特征区别**：`protocol_*`特征通常指传输层协议（TCP、UDP等），应用层协议识别提供了更细粒度的信息。

**3.2 HTTP请求方法统计**

- **特征描述**：统计HTTP请求中各方法的使用频率，如GET、POST、PUT、DELETE等。
- **作用**：
  - **检测Web攻击**：异常的请求方法使用（如大量的POST请求）可能表示SQL注入、跨站脚本等攻击。
  - **分析用户行为模式**：区分正常用户与恶意行为。
- **避免冗余**：
  - **新信息维度**：现有特征中未涉及HTTP方法的统计，该特征提供了新的内容层信息。

**3.3 URL和主机名特征**

- **特征描述**：提取并分析HTTP请求中的URL、主机名，统计其长度、复杂度等。
- **作用**：
  - **检测钓鱼和恶意网站**：异常的URL模式或未知的主机名可能指示钓鱼攻击。
  - **识别数据泄露**：敏感数据可能通过特定的URL参数泄露。
- **避免冗余**：
  - **内容层特征**：该特征关注具体的请求内容，与现有流量统计特征不同。

**3.4 关键字匹配**

- **特征描述**：在数据包负载中搜索特定的恶意关键字或模式，如“login”、“password”、“attack”等。
- **作用**：
  - **检测已知攻击模式**：特定的关键字可能指示攻击尝试或信息收集活动。
  - **辅助行为分析**：结合其他特征，更准确地识别异常活动。
- **避免冗余**：
  - **差异化信息**：关键字匹配提供了对负载内容的直接分析，不与现有统计特征重复。

**3.5 DNS查询特征**

- **特征描述**：分析DNS请求的频率、查询的域名、响应码等。
- **作用**：
  - **检测DNS隧道**：异常高的DNS请求频率可能表示数据泄露或命令控制通信。
  - **识别恶意域名**：查询异常或已知恶意域名可能指示感染或攻击活动。
- **避免冗余**：
  - **特定协议分析**：DNS特征专注于DNS协议的内容，与其他协议的特征区分开。

**3.6 SSL/TLS握手特征**

- **特征描述**：提取SSL/TLS握手信息，如证书信息、加密套件、版本等。
- **作用**：
  - **检测不安全的SSL/TLS版本**：使用过时或已知漏洞的SSL/TLS版本可能被攻击者利用。
  - **识别伪造证书**：异常的证书信息可能指示中间人攻击。
- **避免冗余**：
  - **加密流量分析**：该特征针对加密流量的握手阶段，与一般的流量统计特征不同。

---

**四、对新增DPI特征的深入分析**

**4.1 应用层协议识别**

- **技术实现**：通过解析数据包的负载部分，使用协议特征或端口信息进行协议识别。
- **潜在挑战**：
  - **加密流量**：对于加密的流量，可能无法直接识别应用层协议，需要结合其他特征。
- **与现有特征的关系**：
  - **提供新的分类维度**：有助于模型学习不同协议下的正常和异常行为模式。

**4.2 HTTP请求方法统计**

- **技术实现**：解析HTTP请求，统计各方法的出现次数。
- **潜在挑战**：
  - **高性能需求**：需要实时解析并统计，可能增加系统开销。
- **与现有特征的关系**：
  - **丰富HTTP流量分析**：现有特征主要是统计层面，该特征增加了内容层面的信息。

**4.3 URL和主机名特征**

- **技术实现**：提取HTTP请求中的URL和主机名，计算其长度、字符分布、参数数量等。
- **潜在挑战**：
  - **隐私和合规性**：需要注意对用户隐私的保护，遵守相关法规。
- **与现有特征的关系**：
  - **细化内容分析**：提供了对请求目标的具体分析，有助于识别异常请求。

**4.4 关键字匹配**

- **技术实现**：在数据包负载中进行模式匹配，识别预定义的关键字或正则表达式。
- **潜在挑战**：
  - **性能和准确性**：需要高效的匹配算法，避免误报和漏报。
- **与现有特征的关系**：
  - **直接指示攻击意图**：关键字匹配可以直接揭示攻击者的目的或方法。

**4.5 DNS查询特征**

- **技术实现**：解析DNS请求和响应，统计查询频率、提取域名、记录响应码。
- **潜在挑战**：
  - **域名解析复杂性**：需要处理各种类型的DNS记录和查询方式。
- **与现有特征的关系**：
  - **专注于DNS协议**：弥补现有特征在DNS流量分析上的不足。

**4.6 SSL/TLS握手特征**

- **技术实现**：捕获并解析SSL/TLS握手阶段的数据，提取证书信息和加密参数。
- **潜在挑战**：
  - **加密协议的复杂性**：需要深入理解SSL/TLS协议，处理不同版本和实现。
- **与现有特征的关系**：
  - **增强加密流量分析**：提供对加密流量的可见性，有助于检测利用加密通道的攻击。

---

**五、综合特征集的最终建议**

**5.1 更新后的特征列表**

**数值型特征：**

1. **bytes_per_second**
2. **avg_packet_size**
3. **iat_mean**
4. **iat_std**
5. **payload_entropy**
6. **unique_dst_ratio**
7. **min_packet_size**
8. **packet_size_range**（新特征）：max_packet_size - min_packet_size，捕捉数据包大小的变动范围。
9. **bytes_per_packet**（新特征）：bytes_per_second / packet_frequency，反映平均每个数据包的字节数。
10. **http_method_count_GET**（DPI特征）
11. **http_method_count_POST**（DPI特征）
12. **url_length_mean**（DPI特征）
13. **dns_query_count**（DPI特征）
14. **ssl_tls_version_count**（DPI特征）

**类别型特征（独热编码）：**

1. **protocol_TCP**
2. **protocol_UDP**
3. **protocol_ICMP**
4. **protocol_OTHER**
5. **dst_port_well_known**
6. **dst_port_registered**
7. **dst_port_dynamic**
8. **application_protocol_HTTP**（DPI特征）
9. **application_protocol_FTP**（DPI特征）
10. **application_protocol_DNS**（DPI特征）
11. **application_protocol_SSL_TLS**（DPI特征）

**5.2 特征说明**

- **http_method_count_GET / POST**：统计HTTP请求中GET和POST方法的次数。
- **url_length_mean**：计算HTTP请求中URL的平均长度。
- **dns_query_count**：统计DNS查询的次数。
- **ssl_tls_version_count**：统计不同SSL/TLS版本的使用次数。
- **application_protocol_*（独热编码）**：标识流量所属的应用层协议。

**5.3 避免冗余的策略**

- **特征差异化**：新增的DPI特征提供了内容层和协议层的信息，与现有的统计特征从不同维度描述流量特征。
- **相关性分析**：在添加新特征后，进行相关性分析，确保新特征与现有特征之间的相关系数不超过0.8，避免高度冗余。
- **特征重要性评估**：在模型训练过程中，通过特征重要性指标（如基于随机森林的特征重要性）评估每个特征的贡献，保留对模型有显著贡献的特征。

---

**六、模型训练和评估的注意事项**

**6.1 数据预处理**

- **缺失值处理**：对于无法提取DPI特征的流量（如加密或非HTTP流量），需要合理处理缺失值。
- **特征标准化**：对数值型特征进行标准化或归一化，确保特征尺度一致。
- **类别平衡**：如果攻击流量在数据集中占比较小，可能需要采用过采样、欠采样或使用加权损失函数的方法。

**6.2 模型选择**

- **考虑特征类型的模型**：如梯度提升树、随机森林等能够处理混合类型特征的模型。
- **深度学习模型**：如深度神经网络，可以自动学习复杂的特征关系，但需要更多的数据和计算资源。

**6.3 特征选择和降维**

- **特征重要性分析**：通过模型的特征重要性，筛选出对模型贡献最大的特征。
- **降维方法**：如果特征数量过多，可以考虑使用PCA或其他降维方法，但需注意类别型特征的处理。

**6.4 模型评估**

- **准确率、精确率、召回率、F1值**：全面评估模型的性能，特别关注误报率和漏报率。
- **ROC曲线和AUC值**：评估模型的分类能力。
- **混淆矩阵**：了解模型在各类别上的预测情况。

---

## ==5. 特征提取流程的优化实践==

为了在海量网络流量中实现实时、高效的特征提取,我们在工程实现层面进行了一系列优化,主要包括:

### 5.1 流式处理架构

采用滑动窗口模型,将流量划分为一系列时间窗口,在每个窗口内提取和聚合流级特征。当新的流数据到达时,更新窗口内的流状态和特征统计信息。这种增量式计算避免了重复的特征提取开销。

### 5.2 多线程并行提取

将独立的特征提取任务(如不同流或不同特征)分配给多个工作线程并行处理。主线程负责分发任务和汇总结果,工作线程负责实际的特征计算。通过充分利用多核CPU资源,显著提升了特征提取的并发能力。

### 5.3 流数据的智能过期

及时淘汰长期不活跃的流记录和统计信息,降低内存驻留开销。我们设计了一套基于LRU的流数据缓存淘汰机制,自动检测和清理过期流,确保内存使用始终处于较低水位。

### 5.4 增量式统计更新

对于涉及全局信息的统计特征(如均值、标准差等),采用增量式算法进行计算。每次只需在上一个状态基础上,合并新样本的影响,即可得到更新后的统计量。相比重新计算全局统计量,增量算法在时间和空间复杂度上要优化很多。

### 5.5 I/O 访问优化

频繁的磁盘读写是性能的一大瓶颈。我们通过批量读取流数据、延迟写入提取结果等策略,最小化了I/O 操作的次数。并引入异步I/O 机制,由单独的I/O 线程负责处理读写请求,进一步降低了I/O 延迟。

### 5.6 资源感知的动态调度

考虑到流量的动态波动性,我们设计了一套资源感知的任务调度策略。根据系统的CPU、内存等资源水位,动态调整特征提取的并发度和频率。在流量高峰期确保实时性,在空闲期则节省计算资源,提升了系统的整体弹性。

经过这一系列优化,我们的特征提取模块能够在单机每秒处理百万级网络流的规模,且检测延迟控制在秒级,满足了大规模网络环境下实时异常检测的需求。

## 6. 后续优化方向展望

尽管目前的特征提取方案已经能够支撑较大规模网络的异常检测,但我们仍然识别出一些有待进一步优化的方向:
==**将特征提取代码部署到信创系统（如麒麟系统）进行一些渗透测试，如DDoS、端口扫描、恶意软件传播、异常数据传输、SQL注入等，然后对正常数据和非法数据进行对比分析，从而证明特征有的有效性（在此效果可行的基础上可实施以下几种特征及框架优化）**==

1. ***在上述的基础上，即正常/非正常网络特征提取完毕，并进行归一化后，采用多个模型进行对比训练，并进行模型的准确率、精确率、召回率、F1分数、ROC曲线等指标***
2. ***根据上述的训练情况，可适当对模型加入一些其他特征的分析能力，以提升解决异常检测的能力***

- 加强对加密流量的分析能力,提取更多反映加密通信模式的元数据特征。
- 引入更多环境感知和上下文相关的特征,如结合威胁情报、资产信息等。
- 在流级特征基础上,进一步提取用户和资产级的行为模式特征,实现更高层次的异常检测。
- 改进地理位置信息的提取方法,采用多种IP地址库实现位置解析,提高位置特征的准确性。
- 探索在线学习模式下的动态特征选择方法,实现特征集的自适应演化,应对攻击手段的快速变化。
- 将特征提取过程迁移到大数据平台,利用分布式计算框架(如Spark、Flink、Kafka Streams)实现弹性扩展。

3. ***在上述基础，可以结合监督学习（lstm、gru、1dcnn，对正常/非正常流量学习）和半监督学习方法（自编码器和图神经网络（GNN），只对正常流量进行学习，从而检测异常流量行为）进行对比分析，以及必要的情况可以结合基于规则的异常检测***

- 使用少量标注数据进行初步监督学习，然后通过 **伪标签（pseudo-labeling）** 或 **自训练（self-training）** 逐步将未标注数据引入模型训练。这可以显著提升模型的适应性，尤其是在数据标注困难或不均衡时。

未来我们将持续探索前沿的网络安全分析理论和大数据处理技术,不断迭代优化我们的特征工程方案,力求为日益严峻的网络安全形势提供更加智能、高效、可扩展的检测，以下是目前的特征提取Gui界面。

---

## 正在解决的问题以及预期效果

![1740558790133](image/基于AI的网络入侵检测系统V1.0/1740558790133.png)

###### 1. ==统一时间尺度==

==分析：==
在人工智能训练领域，我们进行一个简单的比喻，比如我们想让模型识别进行动作的识别，那么我们提前就要对要采集的数据进行归一化处理，也就是说要限定一个动作持续的时间，比如都规定在5s左右，那么我就在采集动作的时候做一个5s的动作保存下来就行了，然后在利用机器视觉，提取5s动作的人体骨架坐标作为特征计算，模型根据这些特征训练完成后，我们做一个5s左右的动作，那么模型就能识别出来。因此，该如何精细化的对特攻击特征的数据进行归一化，是接下来要分析思考的。攻击持续时间的差异可能导致数据样本长度不一致，这对于固定输入大小的神经网络模型来说是一个挑战。

==问题思考：==

> 网络攻击可能持续时间不一，模式多样，这使得简单地固定一个时间窗口可能不够灵活。我们需要一个更动态和适应性强的方法，具体分析如以下案例：
>
> - **==微观窗口（1-5秒）==**：捕捉快速、突发性攻击行为，如端口扫描的初始阶段。
> - **==中观窗口（30秒-5分钟）==**：识别持续性攻击模式，如DDoS攻击的主体阶段、密集身份验证尝试、暴力破解攻击。
> - **==宏观窗口（15分钟-1小时）==**：检测缓慢、潜伏的攻击，如APT攻击中的系统扫描、权限提升、后门植入等。
>
> a) 侦察阶段：特征可能包括轻微的端口扫描或探测行为。
> b) 初始访问阶段：可能涉及密集的身份验证尝试或漏洞利用。
> c) 权限提升阶段：特征可能包括异常的系统调用或配置更改。
> d) 横向移动阶段：可能表现为内部网络扫描或异常的远程访问模式。

> 数据格式问题：
> 在保存的CSV输出，部分数据具有以下特征：
>
> 1. 时间间隔不规则（timestamp列）
> 2. 每个时间单位（由"---"分隔）包含的行数不同
> 3. 某些特征（如dst_port）在每个时间单位内可能有多个不同的值
> 4. 有些数值可能是异常的（如负值的flow_duration）

==讨论：==
根据上述的分析，上述对于非正常数据的采集是监督学习，即要获取正常和异常（攻击）数据的标签（对于非监督学习，只用训练大量正常数据，不需要对非法数据进行训练，其根据学习的正常数据就能检测异常的非法数据）。上述的主要挑战在于如何统一不同时间尺度的攻击数据，使其可以被输入到同一个模型中进行训练和检测。

==解决方法：==
通过使用固定大小的滑动窗口，我们可以将不同持续时间的攻击映射到相同维度的特征向量中。这种方法不需要复杂的生命周期识别或手动标注，而是依赖于统计聚合来捕获攻击的特征，其将，将**不同时间长度的网络攻击行为统一在相同的时间尺度内进行特征提取和分析**。
基于以上思考，我提出以下改进方案：

**滑动窗口的解决方案**：滑动窗口将整个网络流量分割为**固定长度的时间片段**。无论攻击行为持续时间长短，滑动窗口通过不断移动窗口，确保每一段时间片段（窗口）都可以得到独立分析。例如：

- 对于短时攻击，滑动窗口可以在短时间内多次滑动，捕捉攻击行为的每一个细节。即使攻击持续时间较短，它也会通过多个重叠窗口不断捕捉到该攻击的特征。
- 对于长时间攻击，滑动窗口可以覆盖整个攻击行为。即便是攻击持续较长时间，每个时间片段的行为特征都可以被捕捉并输入到模型中。

通过这种方式，滑动窗口**将各种攻击行为“切割”成一致的时间片段**，从而归一化不同时间长度的攻击，解决了时间尺度上的不一致问题。

**具体设计思路如下：**
为了==固定数据采集频率==，我想采用以下方法进行数据采集：

- 固定时间间隔的高频采样 + 动态聚合：
  - 以固定的高频率（如每0.5秒）采集原始数据。
  - 动态聚合到1秒间隔，保证每秒都有数据点。
  - 对于1分钟和5分钟窗口，使用固定数量的数据点。
- 数据处理流程：
  - 高频采样：每0.5秒采集一次原始数据。
  - 1秒聚合：将每两个0.5秒的数据点聚合为1个1秒数据点。
  - 窗口填充：对于1分钟窗口，使用60个1秒数据点；对于5分钟窗口，使用300个1秒数据点（为了让数据能归一化进行如模型训练，将所有数据都分为1min、5min）。
  - 不足时补充：如果数据不足，使用最近的有效数据点填充。
  - 过多时裁剪：如果数据过多，保留最新的数据点。

为了==固定数据格式的统一==，以及机器学习更好的进行训练，采用以下方法：

- 时间一致性：通过重采样到固定的时间间隔（例如1分钟），我们确保了每个样本代表相同的时间段。
- 特征聚合：对于每个特征，我们使用适当的聚合函数。例如，对于dst_port，我们选择最常见的值；对于数值型特征，我们计算平均值和最大值。
- 处理多值：通过聚合，我们解决了在同一时间单位内可能出现多个不同值的问题。
- 异常值处理：通过使用均值和最大值等统计量，我们减少了异常值的影响。
- 保留信息：通过计算多个统计量（如均值和最大值），我们保留了更多的原始数据信息。
- 固定特征数：处理后的数据集将有固定数量的特征，适合机器学习模型的输入。

**专业合理的解决方案**

综合以上分析，建议采用 **多尺度固定时间窗口** 与 **滑动窗口相结合** 的方案。具体如下：

1. **多尺度固定时间窗口**

   - **设置多个固定时间窗口**：例如，1 分钟和 5 分钟。
   - **同步进行特征聚合**：对于每个时间窗口，分别进行特征提取和聚合。
2. **滑动窗口机制**

   - **对于短时间窗口（1 分钟）**，可以采用滑动窗口机制，步长设置为 10 秒或更小。
   - **对于长时间窗口（5 分钟）**，可以采用非重叠的固定窗口，减少计算量。
3. **特征一致性**

   - **统一特征集合**：确保在不同时间窗口下，特征集合一致，便于模型训练和比较。
   - **特征归一化**：对特征进行归一化处理，消除量纲差异。
4. **数据采集策略**

   - **高频数据采集**：确保数据的完整性，避免数据丢失。
   - **实时处理与存储**：采用多线程或异步方式，实时处理数据，避免数据积压。
5. **模型训练与检测**

   - **多模型融合**：针对不同时间尺度的特征，训练对应的模型。
   - **联合检测**：在实际检测中，结合多个模型的输出，提高检测的准确率和鲁棒性。

![1740558754439](image/基于AI的网络入侵检测系统V1.0/1740558754439.png)

![1740558764006](image/基于AI的网络入侵检测系统V1.0/1740558764006.png)

##### 2.**==模拟攻击涉及场景==**

a) 基础扫描和探测：

- 使用Nmap进行TCP SYN扫描
- 使用Nmap进行UDP扫描
- 使用Nmap进行操作系统指纹识别

b) 拒绝服务攻击：

- 使用hping3进行SYN洪水攻击
- 使用LOIC工具进行HTTP洪水攻击

c) 暴力破解：

- 使用Hydra对SSH服务进行密码猜测
- 使用Medusa对Web登录页面进行暴力破解

d) 漏洞利用：

- 使用Metasploit框架exploit一个已知的Web服务漏洞
- 模拟一个缓冲区溢出攻击

e) 高级持续性威胁（APT）模拟（设计一个多阶段的攻击链，包括初始侦察、建立立足点、权限提升和数据渗出）：

- 阶段1：使用社会工程学技巧获取初始访问（模拟）
- 阶段2：使用定制的后门程序建立持久性
- 阶段3：使用提权漏洞提升权限
- 阶段4：在内网进行横向移动（使用Mimikatz等工具）
- 阶段5：数据渗透（大量数据传输）
- **==执行步骤：==**

  1. 在隔离的测试环境中部署目标系统。
  2. 启动背景流量生成器（部署在信创系统中），模拟正常网络活动。
  3. 执行预定义的攻击脚本，模拟不同类型和阶段的攻击。
  4. 在整个过程中，使用上述的自动采集系统收集数据。

**==上述针对非正常网络的数据采集过程，可能会出现以下问题或困难：==**

1. **实时性与网络负载的影响**：在进行数据采集时，网络负载和采集系统的资源消耗是重要的考虑因素。特别是在面对高频攻击（如DDoS攻击）或大规模扫描时，系统可能会面临性能瓶颈，导致部分数据丢失或采集滞后。

##### 3.**==预期效果==**

###### 1. 特征提取阶段

理论基础： 特征提取是机器学习和数据分析的关键步骤，尤其在网络安全领域更为重要。有效的特征提取可以显著提高异常检测的准确性和效率。在网络入侵检测中，我们需要捕捉网络流量的多个维度，包括时间特征、统计特征和行为特征。

实施方法：

1. 滑动窗口机制：实现可变大小的滑动窗口（1分钟、5分钟），以捕捉不同时间尺度的网络行为。
2. 高频采样策略：每0.5秒采样一次，确保捕获短暂的异常行为。
3. 动态聚合：将0.5秒的样本动态聚合到1秒间隔，平衡细粒度和计算效率。
4. 数据补充和裁剪：对于不足的数据进行插值补充，过多的数据进行智能裁剪，确保数据的一致性。
5. 时序特征归一化：使用如Z-score或MinMaxScaler缩放等方法，使不同尺度的特征可比。

预期结果：

1. 高质量的特征集：包括基本网络特征（如数据包大小、流量速率）和高级特征（如熵值、协议分布）。
2. 多尺度表示：能够同时捕获微观（秒级）、中观（分钟级）的网络行为。
3. 实时处理能力：系统能够以接近实时的速度处理incoming网络流量，延迟不超过1秒。
4. 特征的稳定性：通过归一化和聚合，得到的特征集在不同网络环境中具有良好的稳定性和可比性。

###### 2. 攻击数据采集阶段

理论基础： 高质量的攻击数据集对于训练有效的入侵检测模型至关重要。这些数据需要涵盖各种类型的网络攻击，同时要考虑到真实网络环境中攻击的多样性和复杂性。

实施方法：

1. 正常数据采集：在1分钟和5分钟两个时间维度采集正常网络流量。
2. 短时攻击数据：针对如端口扫描、DoS攻击等，采用1分钟时间窗口进行采集。
3. 长时攻击数据：针对APT攻击、持续的暴力破解等，采用5分钟时间窗口进行采集。
4. 攻击场景模拟：使用专业工具（如Nmap、Metasploit）模拟各种攻击类型。
5. 数据标注：精确标注每个数据包或流的类型（正常/攻击类型）。

预期结果：

1. 综合数据集：包含正常流量和多种类型攻击的大规模数据集。
2. 时间多样性：数据集涵盖不同时间尺度（1分钟和5分钟）的网络行为。
3. 攻击多样性：数据集包含从简单的端口扫描到复杂的APT攻击在内的多种攻击类型。
4. 高质量标注：每个数据样本都有准确的标签，标明其是正常流量还是特定类型的攻击。
5. 真实性：采集的数据尽可能接近真实网络环境中的流量特征。

###### 3. 模型训练阶段

理论基础： 网络入侵检测可以视为一个时序异常检测问题。通过结合监督学习和非监督学习方法，我们可以构建一个既能识别已知攻击模式，又能检测未知异常的强大系统。

实施方法：

1. 监督学习：
   - 使用LSTM和GRU等循环神经网络模型，捕捉网络流量的时序依赖，并采用**参数调优**: 使用网格搜索、随机搜索或贝叶斯优化，寻找最佳模型参数。
   - **训练集、验证集、测试集**：按照6:2:2或其他比例划分数据，确保模型评估的可靠性。
   - 尝试决策树、随机森林、支持向量机、神经网络等模型，选择效果最佳的。
   - 实现CNN-1D模型，有效提取局部时间特征。
2. 非监督学习：
   - 实现自编码器，学习正常网络行为的潜在表示。
   - 使用隔离森林等算法，检测异常点。
3. 集成方法：
   - 设计投票机制，综合多个模型的预测结果。
   - 实现基于置信度的加权集成策略。
4. 模型评估：
   - 使用交叉验证评估模型的泛化能力。
   - 计算准确率、精确率、召回率、F1分数等指标。
   - 绘制ROC曲线和PR曲线，全面评估模型性能。
   - **k折交叉验证**：使用交叉验证评估模型的泛化能力，避免过拟合。

预期结果：

1. 高性能模型：在测试集上达到>95%的准确率和>90%的F1分数。
2. 实时检测：模型能够在<100ms内完成单个数据点的分类。
3. 可解释性：通过注意力机制或SHAP值分析，提供模型决策的可解释性。
4. 鲁棒性：模型在面对slight distribution shift时仍能保持稳定表现。
5. 适应性：通过在线学习机制，模型能够适应网络环境的动态变化。

##### 4.**==整体流程图==**

![[Pasted image 20240918164605.png]]

这个流程图详细展示了从数据采集到模型部署的整个过程。以下是对每个步骤的简要说明：

1. 实时网络数据采集：持续捕获网络流量数据。
2. 特征提取：从原始数据中提取相关特征。
3. 滑动窗口处理：使用1分钟和5分钟的滑动窗口处理数据。
4. 数据归一化：对提取的特征进行归一化处理。
5. 数据分组：将数据分成每组12个数据点。
6. 数据集划分：将数据集分为训练集、验证集和测试集。
7. 模型选择：选择适合的机器学习或深度学习模型。
8. 模型训练：使用训练数据训练选定的模型。
9. 模型评估：使用验证集评估模型性能。
10. 决策点：判断模型性能是否满足要求。如果不满足，进行模型优化；如果满足，继续下一步。
11. 模型优化：如果需要，对模型进行调整和优化。
12. 模型部署：将训练好的模型部署到生产环境。
13. 实时数据处理：处理实时流入的网络数据。
14. 实时预测：使用部署的模型对实时数据进行预测。
15. 告警系统：根据预测结果触发相应的告警。
16. 模型监控和更新：持续监控模型性能，并在需要时更新模型。
