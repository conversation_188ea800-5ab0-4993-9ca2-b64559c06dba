https://gumxghvrlrtp.eu-central-1.clawcloudrun.com

sk-18157194395

---

openai格式

https://v3.crond.dev/v1

sk-Bt8M4H0XAiJsRJXai2QuYuK63NNDxpJbHIYBxHuyoBtX5Rqc

---

claude-3-7-sonnet-20250219-thinking

sk-sH12D7cfor80eilt2wtCaQ4YzGz84199lqdpg11xUFcqV8aG

https://api-demo.zyox.top/v1

o3-mini-2025-01-31-high

---

sk-bppgdKIAjRpR9Gedd5XHD8q6YIknvTL09l5GqFWY6I3U7Q8L

https://x666.me/v1

---

咸鱼key

使用教程及报错解决方法：
https://dcnyq6z2rpri.feishu.cn/wiki/RePpwtmwwiVF0kkJfqScy7f8nSg

gemini-2.5-pro-preview-03-25

次卡

https://api.sonetto.top/v1

sk-dXW96eS3nmPPBfJqDzQeLDsDEuJf8qWQKe3qb1xvz7FWIHNM  （抽奖抽中的）

sk-1fYJkTWf5Ml1YozSwgElvzUfG3LswQnfwjAOGryECvdIVSfy    （月卡换的）

---

sk-ANPuVdkgnqtlQLjUTV7qEKm8j2eC93x9ua5gOj1QWMrd6vKT

https://happyapi.org/v1

---

竞技场a2pi

BaseURL:[https://lma2api.aytsao.cn](https://lma2api.aytsao.cn/)
Key:linux.do

---

# 总体提问方针策略如下：

关于roocode的使用方法（配合memory bank提示词），每次从GitHub上找到需求的项目，然后打开SPARC模式，在该模式下让其预览你的项目，并构建memory bank文件夹（该文件夹中会记录你所有的操作、目标、修改等内容），之后可以去context 7网站，搜索关于该项目需求的依赖说明内容，将其添加到memory bank中，这样项目在增加新的功能的时候，就能根据依赖说明文档，进行更好的操作和匹配，从而达到功能和依赖配置的一致性。
1.在每次开启新的窗口时，都要ai了解你的项目目录、结构、内容信息；
2.每次提出新的需求、功能，都要列出详细的具体的可行计划，之后再对计划进行审查，保持一致性；
3.每次进行计划的实施的之后，都要进行执行后代码的审查，从而确保一致性；

---

# 以下为部分策略提问prompt：

分析我目前的代码逻辑以及结构，尽可能的详细且具体

我希望当前的代码能够在我运行.\build.bat --debug时，能够现实exe运行的调式窗口信息，请分析目前的项目是否能实现，请确保能够实现最基础的功能

---

按照当前的项目，运行后的exe依旧直接退出，请进一步分析问题所在，最好写一个专门的日志文件能进行记录，当我运行build.bat的时候可以进行选择（.\build.bat --debug），从而控制是否生成调试的exe，请分析如何实现

---

我希望只有一个bat文件即可，我将原本的build.bat删除了，我希望在现有的build__debug.bat的基础上能够进行命令行的控制，从而决定是否采用调试模式，请将其重命名为build.bat，当我运行build.bat --debug时，exe为调试模式，如果单纯的运行build.bat的话，那么就是不带有调试信息的，请分析如何实现上述目标，请制定计划

---

请根据上述的未实现的功能，结合目前的代码，写出详细的实施计划，尽可能的具体且可实践性

---

请根据上述的计划进行修复，注意细节，逻辑清晰，要清楚自己的每次操作的问题描述

---

最新版本的代码运行出现以下问题，结合我的代码功能以及逻辑，分析问题所在，并制定详细且具体的修复计划（并对计划进行审查，是否正确可行）：

---

请将问题进行描述，输出完整的修复计划（详细且具体），并以结构化的prompt输出给我

---

请检查我的logs文件下的RhMLWyfS_debug_log_2025-05-11_17-16-55文件，进行问题的分析，并列出详细且具体的修复计划，尽可能专业详细且具体，并对计划进行审查，分析可行性

---

请你运行代码，先build，之后运行生成的exe文件，查看日志进行分析

---

将上述的详细的修复计划完整的更新到memory bank中，并备注时间戳，请分析将其添加到哪个md文档中比较合适，其中这些内容是不是需要添加“TODO”等类似的大标题，这样更为专业清晰一些

---

根据上述的分析，让我们先来完成2.1部分需要修复，即：
2.1 ModuleStompingAllocator 高级功能缺失 (memory-bank/progress.md:228)。
请依据“上述的2.1修复计划”，进行深度优化与细化，输出一份高度可操作的执行方案：首先，将计划中的每个阶段和任务彻底分解为最小可执行的子任务单元，明确各子任务的具体操作步骤、预期成果及验收标准；其次，对每个子任务进行全面的可行性审查，评估技术实现路径、外部依赖（参考memory bank中的rust、cargo依赖），识别潜在风险与障碍，并制定相应的缓解策略与应急预案；再次，针对计划中存在的关键决策点或技术难题，从技术选型、整体影响等多个维度展开对比分析，论证并择定最优解决方案（如果修复过程中反复出现无法修复的问题，那么就考虑备用方案）；最后，严格确保所有子任务与整体修复目标的高度一致性，并针对代码层面的变更，详细规划验证流程，以保障代码修改的精确性、功能实现的完整性、以及新旧代码与模块间的无缝集成和系统整体稳定性。

---

先对我的项目进行分析，特别是目前依旧的功能及其代码，然后主要分析以下来自 `productContext.md` 的任务 **TASK-P0.1.6**，并为其设计详细、具体且可行的子任务。然后，为这些新设计的子任务制定详细的实施计划，并对整个计划进行审查。目标是尽可能地详细，请进行详细的

---

将上述遇到的问题进行分析总结，以及目前任务的进度如何、项目大背景等，尽可能的描述详细且具体，并整理为标准prompt的形式输出到markdown代码框中，请确保所有信息真实、准确，并使用清晰、专业的语言进行表述。

---

请对我指定的memory bank项目进行全面的测试覆盖分析和后续测试规划。你需要结合我提供的项目代码及当前的测试完成度信息，精确识别已测试和未测试的模块，尽可能的详细且具体，总体的方针策略是要完成并验证P0相关的模块；此外，如果你在测试分析的过程中，遇到一个反复无法解决的问题，需要你将目前的问题尽可能的描述清楚，即背景、目标等细节，尽可能的详细且具体，包括已经尝试了哪几种解决方法，出现哪些问题，将这些信息整理为专业的prompt描述，并输出到markdown代码框中（我回去进行相关的问题分析，分析完毕之后，你再进行更为具体的思考，从而找到最佳解决方案）。
